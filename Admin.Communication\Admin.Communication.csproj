<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <NoWarn>1701;1702;1591;8618;8602;</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MQTTnet" Version="5.0.1.1416" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Admin.Core\Admin.Core.csproj" />
    <ProjectReference Include="..\Admin.Multiplex.Contracts\Admin.Multiplex.Contracts.csproj" />
    <ProjectReference Include="..\Admin.BackgroundService\Admin.BackgroundService.csproj" />
    <ProjectReference Include="..\Admin.SqlSugar\Admin.SqlSugar.csproj" />
  </ItemGroup>

</Project> 