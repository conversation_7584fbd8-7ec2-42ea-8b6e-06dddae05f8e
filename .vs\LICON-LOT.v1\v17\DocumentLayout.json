{"Version": 1, "WorkspaceRootPath": "D:\\code projects\\purest-admin-main\\api\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{C0E0D16E-704E-4D86-AC86-F20BB00FCD46}|Admin.Application\\Admin.Application.csproj|d:\\code projects\\purest-admin-main\\api\\admin.application\\modbusservices\\modbusinstructionmanagementservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C0E0D16E-704E-4D86-AC86-F20BB00FCD46}|Admin.Application\\Admin.Application.csproj|solutionrelative:admin.application\\modbusservices\\modbusinstructionmanagementservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4955DD52-EF05-4A55-88EB-3BC3A86EE577}|Admin.SqlSugar\\Admin.SqlSugar.csproj|d:\\code projects\\purest-admin-main\\api\\admin.sqlsugar\\entity\\business\\lot\\modelpropertyentity.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4955DD52-EF05-4A55-88EB-3BC3A86EE577}|Admin.SqlSugar\\Admin.SqlSugar.csproj|solutionrelative:admin.sqlsugar\\entity\\business\\lot\\modelpropertyentity.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4955DD52-EF05-4A55-88EB-3BC3A86EE577}|Admin.SqlSugar\\Admin.SqlSugar.csproj|d:\\code projects\\purest-admin-main\\api\\admin.sqlsugar\\entity\\business\\lot\\deviceparaentity.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4955DD52-EF05-4A55-88EB-3BC3A86EE577}|Admin.SqlSugar\\Admin.SqlSugar.csproj|solutionrelative:admin.sqlsugar\\entity\\business\\lot\\deviceparaentity.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{86433DE6-50BB-4ED0-9BBB-2FCB8D59B121}|Admin.Communication\\Admin.Communication.csproj|d:\\code projects\\purest-admin-main\\api\\admin.communication\\modbus\\models\\instructionschedulestate.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{86433DE6-50BB-4ED0-9BBB-2FCB8D59B121}|Admin.Communication\\Admin.Communication.csproj|solutionrelative:admin.communication\\modbus\\models\\instructionschedulestate.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{86433DE6-50BB-4ED0-9BBB-2FCB8D59B121}|Admin.Communication\\Admin.Communication.csproj|d:\\code projects\\purest-admin-main\\api\\admin.communication\\modbus\\models\\modbusresponse.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{86433DE6-50BB-4ED0-9BBB-2FCB8D59B121}|Admin.Communication\\Admin.Communication.csproj|solutionrelative:admin.communication\\modbus\\models\\modbusresponse.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4955DD52-EF05-4A55-88EB-3BC3A86EE577}|Admin.SqlSugar\\Admin.SqlSugar.csproj|d:\\code projects\\purest-admin-main\\api\\admin.sqlsugar\\entity\\business\\lot\\deviceinstructionentity.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4955DD52-EF05-4A55-88EB-3BC3A86EE577}|Admin.SqlSugar\\Admin.SqlSugar.csproj|solutionrelative:admin.sqlsugar\\entity\\business\\lot\\deviceinstructionentity.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4955DD52-EF05-4A55-88EB-3BC3A86EE577}|Admin.SqlSugar\\Admin.SqlSugar.csproj|d:\\code projects\\purest-admin-main\\api\\admin.sqlsugar\\entity\\business\\lot\\modelinstructionentity.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4955DD52-EF05-4A55-88EB-3BC3A86EE577}|Admin.SqlSugar\\Admin.SqlSugar.csproj|solutionrelative:admin.sqlsugar\\entity\\business\\lot\\modelinstructionentity.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4955DD52-EF05-4A55-88EB-3BC3A86EE577}|Admin.SqlSugar\\Admin.SqlSugar.csproj|d:\\code projects\\purest-admin-main\\api\\admin.sqlsugar\\admin.sqlsugar.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{4955DD52-EF05-4A55-88EB-3BC3A86EE577}|Admin.SqlSugar\\Admin.SqlSugar.csproj|solutionrelative:admin.sqlsugar\\admin.sqlsugar.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{7E7B7A8C-AC37-48F3-97F5-DC5AAC65620B}|Admin.BackgroundService\\Admin.BackgroundService.csproj|d:\\code projects\\purest-admin-main\\api\\admin.backgroundservice\\workers\\clearrequestlogworker.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7E7B7A8C-AC37-48F3-97F5-DC5AAC65620B}|Admin.BackgroundService\\Admin.BackgroundService.csproj|solutionrelative:admin.backgroundservice\\workers\\clearrequestlogworker.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 16, "Children": [{"$type": "Bookmark", "Name": "ST:129:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:131:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:130:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:129:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:134:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:135:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:137:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:131:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:130:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "ModelPropertyEntity.cs", "DocumentMoniker": "D:\\code projects\\purest-admin-main\\api\\Admin.SqlSugar\\Entity\\Business\\LOT\\ModelPropertyEntity.cs", "RelativeDocumentMoniker": "Admin.SqlSugar\\Entity\\Business\\LOT\\ModelPropertyEntity.cs", "ToolTip": "D:\\code projects\\purest-admin-main\\api\\Admin.SqlSugar\\Entity\\Business\\LOT\\ModelPropertyEntity.cs", "RelativeToolTip": "Admin.SqlSugar\\Entity\\Business\\LOT\\ModelPropertyEntity.cs", "ViewState": "AgIAABUAAAAAAAAAAADwvykAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T13:40:59.931Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "DeviceParaEntity.cs", "DocumentMoniker": "D:\\code projects\\purest-admin-main\\api\\Admin.SqlSugar\\Entity\\Business\\LOT\\DeviceParaEntity.cs", "RelativeDocumentMoniker": "Admin.SqlSugar\\Entity\\Business\\LOT\\DeviceParaEntity.cs", "ToolTip": "D:\\code projects\\purest-admin-main\\api\\Admin.SqlSugar\\Entity\\Business\\LOT\\DeviceParaEntity.cs", "RelativeToolTip": "Admin.SqlSugar\\Entity\\Business\\LOT\\DeviceParaEntity.cs", "ViewState": "AgIAAC0AAAAAAAAAAADwvz8AAAAOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T13:38:42.614Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "InstructionScheduleState.cs", "DocumentMoniker": "D:\\code projects\\purest-admin-main\\api\\Admin.Communication\\Modbus\\Models\\InstructionScheduleState.cs", "RelativeDocumentMoniker": "Admin.Communication\\Modbus\\Models\\InstructionScheduleState.cs", "ToolTip": "D:\\code projects\\purest-admin-main\\api\\Admin.Communication\\Modbus\\Models\\InstructionScheduleState.cs", "RelativeToolTip": "Admin.Communication\\Modbus\\Models\\InstructionScheduleState.cs", "ViewState": "AgIAADMAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T13:28:12.116Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "ModbusResponse.cs", "DocumentMoniker": "D:\\code projects\\purest-admin-main\\api\\Admin.Communication\\Modbus\\Models\\ModbusResponse.cs", "RelativeDocumentMoniker": "Admin.Communication\\Modbus\\Models\\ModbusResponse.cs", "ToolTip": "D:\\code projects\\purest-admin-main\\api\\Admin.Communication\\Modbus\\Models\\ModbusResponse.cs", "RelativeToolTip": "Admin.Communication\\Modbus\\Models\\ModbusResponse.cs", "ViewState": "AgIAAAAAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T13:28:10.081Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "ModelInstructionEntity.cs", "DocumentMoniker": "D:\\code projects\\purest-admin-main\\api\\Admin.SqlSugar\\Entity\\Business\\LOT\\ModelInstructionEntity.cs", "RelativeDocumentMoniker": "Admin.SqlSugar\\Entity\\Business\\LOT\\ModelInstructionEntity.cs", "ToolTip": "D:\\code projects\\purest-admin-main\\api\\Admin.SqlSugar\\Entity\\Business\\LOT\\ModelInstructionEntity.cs", "RelativeToolTip": "Admin.SqlSugar\\Entity\\Business\\LOT\\ModelInstructionEntity.cs", "ViewState": "AgIAACoAAAAAAAAAAADwvzgAAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T09:59:44.787Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "DeviceInstructionEntity.cs", "DocumentMoniker": "D:\\code projects\\purest-admin-main\\api\\Admin.SqlSugar\\Entity\\Business\\LOT\\DeviceInstructionEntity.cs", "RelativeDocumentMoniker": "Admin.SqlSugar\\Entity\\Business\\LOT\\DeviceInstructionEntity.cs", "ToolTip": "D:\\code projects\\purest-admin-main\\api\\Admin.SqlSugar\\Entity\\Business\\LOT\\DeviceInstructionEntity.cs", "RelativeToolTip": "Admin.SqlSugar\\Entity\\Business\\LOT\\DeviceInstructionEntity.cs", "ViewState": "AgIAAB4AAAAAAAAAAADwvzUAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T09:55:01.55Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "Admin.SqlSugar.csproj", "DocumentMoniker": "D:\\code projects\\purest-admin-main\\api\\Admin.SqlSugar\\Admin.SqlSugar.csproj", "RelativeDocumentMoniker": "Admin.SqlSugar\\Admin.SqlSugar.csproj", "ToolTip": "D:\\code projects\\purest-admin-main\\api\\Admin.SqlSugar\\Admin.SqlSugar.csproj", "RelativeToolTip": "Admin.SqlSugar\\Admin.SqlSugar.csproj", "ViewState": "AgIAAAAAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-07-24T09:54:52.699Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "ModbusInstructionManagementService.cs", "DocumentMoniker": "D:\\code projects\\purest-admin-main\\api\\Admin.Application\\ModbusServices\\ModbusInstructionManagementService.cs", "RelativeDocumentMoniker": "Admin.Application\\ModbusServices\\ModbusInstructionManagementService.cs", "ToolTip": "D:\\code projects\\purest-admin-main\\api\\Admin.Application\\ModbusServices\\ModbusInstructionManagementService.cs", "RelativeToolTip": "Admin.Application\\ModbusServices\\ModbusInstructionManagementService.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAAABsAAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T09:36:34.409Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "ClearRequestLogWorker.cs", "DocumentMoniker": "D:\\code projects\\purest-admin-main\\api\\Admin.BackgroundService\\Workers\\ClearRequestLogWorker.cs", "RelativeDocumentMoniker": "Admin.BackgroundService\\Workers\\ClearRequestLogWorker.cs", "ToolTip": "D:\\code projects\\purest-admin-main\\api\\Admin.BackgroundService\\Workers\\ClearRequestLogWorker.cs", "RelativeToolTip": "Admin.BackgroundService\\Workers\\ClearRequestLogWorker.cs", "ViewState": "AgIAAAAAAAAAAAAAAADwvxQAAABOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T09:29:58.805Z"}]}]}]}