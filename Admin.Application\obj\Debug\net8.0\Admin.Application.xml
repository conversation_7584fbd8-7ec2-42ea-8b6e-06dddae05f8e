<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Admin.Application</name>
    </assembly>
    <members>
        <member name="T:Admin.Application.AlarmServices.AlarmEventService">
            <summary>
            告警事件服务
            </summary>
        </member>
        <member name="M:Admin.Application.AlarmServices.AlarmEventService.#ctor(Admin.Communication.Alarm.Services.AlarmEventGeneratorService)">
            <summary>
            告警事件服务
            </summary>
        </member>
        <member name="M:Admin.Application.AlarmServices.AlarmEventService.SyncSystemAlarmEventsAsync">
            <summary>
            同步系统告警事件
            重新生成所有设备的告警事件，用于参数配置变更后的事件同步
            </summary>
            <returns>同步结果</returns>
        </member>
        <member name="T:Admin.Application.AlarmServices.AlarmEventSyncResult">
            <summary>
            告警事件同步结果
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.AlarmEventSyncResult.Success">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.AlarmEventSyncResult.TotalEventCount">
            <summary>
            生成的事件总数
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.AlarmEventSyncResult.Message">
            <summary>
            结果消息
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.AlarmEventSyncResult.SyncTime">
            <summary>
            同步时间
            </summary>
        </member>
        <member name="T:Admin.Application.AlarmServices.AlarmHistoryService">
            <summary>
            告警历史服务
            </summary>
        </member>
        <member name="M:Admin.Application.AlarmServices.AlarmHistoryService.#ctor(SqlSugar.ISqlSugarClient)">
            <summary>
            告警历史服务
            </summary>
        </member>
        <member name="M:Admin.Application.AlarmServices.AlarmHistoryService.GetPendingConfirmationAlarmsAsync(Admin.Application.AlarmServices.Dto.AlarmHistoryPagedInput)">
            <summary>
            获取待确认告警
            </summary>
            <param name="input">分页参数</param>
            <returns>待确认告警列表</returns>
        </member>
        <member name="M:Admin.Application.AlarmServices.AlarmHistoryService.GetConfirmedAlarmsAsync(Admin.Application.AlarmServices.Dto.AlarmHistoryPagedInput)">
            <summary>
            获取已确认告警
            </summary>
            <param name="input">分页参数</param>
            <returns>已确认告警列表</returns>
        </member>
        <member name="M:Admin.Application.AlarmServices.AlarmHistoryService.GetUnconfirmedButReleasedAlarmsAsync(Admin.Application.AlarmServices.Dto.AlarmHistoryPagedInput)">
            <summary>
            获取未确认但已解除的告警
            </summary>
            <param name="input">分页参数</param>
            <returns>未确认但已解除的告警列表</returns>
        </member>
        <member name="M:Admin.Application.AlarmServices.AlarmHistoryService.ConfirmAlarmsAsync(Admin.Application.AlarmServices.Dto.ConfirmAlarmInput)">
            <summary>
            确认告警
            </summary>
            <param name="input">确认输入</param>
            <returns>确认结果</returns>
        </member>
        <member name="M:Admin.Application.AlarmServices.AlarmHistoryService.GetAlarmLevelDescription(System.Int32)">
            <summary>
            获取告警级别描述
            </summary>
        </member>
        <member name="T:Admin.Application.AlarmServices.Dto.AlarmHistoryPagedInput">
            <summary>
            告警历史分页查询输入
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.Dto.AlarmHistoryPagedInput.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.Dto.AlarmHistoryPagedInput.DeviceName">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.Dto.AlarmHistoryPagedInput.AlarmType">
            <summary>
            告警类型 (1:通讯失败, 2:参数超限)
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.Dto.AlarmHistoryPagedInput.AlarmLevel">
            <summary>
            告警级别 (1：紧急 2：严重 3：一般 4：预警)
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.Dto.AlarmHistoryPagedInput.AlarmStatus">
            <summary>
            告警状态 (1:待确认, 2:已确认, 3:待处理, 4:已处理)
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.Dto.AlarmHistoryPagedInput.IsReleased">
            <summary>
            是否已解除
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.Dto.AlarmHistoryPagedInput.StartTime">
            <summary>
            开始时间
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.Dto.AlarmHistoryPagedInput.EndTime">
            <summary>
            结束时间
            </summary>
        </member>
        <member name="T:Admin.Application.AlarmServices.Dto.AlarmHistoryOutput">
            <summary>
            告警历史输出
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.Dto.AlarmHistoryOutput.Id">
            <summary>
            告警ID
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.Dto.AlarmHistoryOutput.EventId">
            <summary>
            事件ID
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.Dto.AlarmHistoryOutput.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.Dto.AlarmHistoryOutput.DeviceName">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.Dto.AlarmHistoryOutput.AlarmDescription">
            <summary>
            告警描述
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.Dto.AlarmHistoryOutput.AlarmValue">
            <summary>
            告警值
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.Dto.AlarmHistoryOutput.AlarmLevel">
            <summary>
            告警级别
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.Dto.AlarmHistoryOutput.AlarmLevelDescription">
            <summary>
            告警级别描述
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.Dto.AlarmHistoryOutput.AlarmStatus">
            <summary>
            告警状态
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.Dto.AlarmHistoryOutput.AlarmStatusDescription">
            <summary>
            告警状态描述
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.Dto.AlarmHistoryOutput.AlarmTime">
            <summary>
            告警时间
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.Dto.AlarmHistoryOutput.ConfirmedPeople">
            <summary>
            告警确认人
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.Dto.AlarmHistoryOutput.ConfirmedTime">
            <summary>
            确认时间
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.Dto.AlarmHistoryOutput.ReleaseTime">
            <summary>
            解除时间
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.Dto.AlarmHistoryOutput.ReleaseValue">
            <summary>
            解除数值
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.Dto.AlarmHistoryOutput.ReleaseDescription">
            <summary>
            解除描述
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.Dto.AlarmHistoryOutput.IsReleased">
            <summary>
            是否已解除
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.Dto.AlarmHistoryOutput.DurationMinutes">
            <summary>
            告警持续时间（分钟）
            </summary>
        </member>
        <member name="T:Admin.Application.AlarmServices.Dto.ConfirmAlarmInput">
            <summary>
            确认告警输入
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.Dto.ConfirmAlarmInput.AlarmIds">
            <summary>
            告警ID列表
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.Dto.ConfirmAlarmInput.ConfirmedPeople">
            <summary>
            确认人
            </summary>
        </member>
        <member name="T:Admin.Application.AlarmServices.Dto.AlarmStatisticsOutput">
            <summary>
            告警统计输出
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.Dto.AlarmStatisticsOutput.TotalCount">
            <summary>
            总告警数
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.Dto.AlarmStatisticsOutput.PendingConfirmationCount">
            <summary>
            待确认数量
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.Dto.AlarmStatisticsOutput.ConfirmedCount">
            <summary>
            已确认数量
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.Dto.AlarmStatisticsOutput.PendingProcessingCount">
            <summary>
            待处理数量
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.Dto.AlarmStatisticsOutput.ProcessedCount">
            <summary>
            已处理数量
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.Dto.AlarmStatisticsOutput.UnreleasedCount">
            <summary>
            未解除数量
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.Dto.AlarmStatisticsOutput.ReleasedCount">
            <summary>
            已解除数量
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.Dto.AlarmStatisticsOutput.CommunicationFailureCount">
            <summary>
            通讯失败数量
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.Dto.AlarmStatisticsOutput.ParameterOutOfRangeCount">
            <summary>
            参数超限数量
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.Dto.AlarmStatisticsOutput.EmergencyCount">
            <summary>
            紧急级别数量
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.Dto.AlarmStatisticsOutput.CriticalCount">
            <summary>
            严重级别数量
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.Dto.AlarmStatisticsOutput.NormalCount">
            <summary>
            一般级别数量
            </summary>
        </member>
        <member name="P:Admin.Application.AlarmServices.Dto.AlarmStatisticsOutput.WarningCount">
            <summary>
            预警级别数量
            </summary>
        </member>
        <member name="T:Admin.Application.AuthServices.AuthService">
            <summary>
            用户授权服务
            </summary>
        </member>
        <member name="M:Admin.Application.AuthServices.AuthService.#ctor(Admin.Multiplex.Contracts.IAdminUser.OAuth2.IOAuth2UserManager,Microsoft.AspNetCore.SignalR.IHubContext{Admin.Multiplex.AdminUser.AuthorizationHub,Admin.Multiplex.Contracts.IAdminUser.OAuth2.IAuthorizationClient},Microsoft.Extensions.Configuration.IConfiguration,Admin.Multiplex.Contracts.IAdminUser.IAdminToken,Microsoft.AspNetCore.Http.IHttpContextAccessor,SqlSugar.ISqlSugarClient,Admin.Multiplex.Contracts.IAdminUser.ICurrentUser)">
            <summary>
            用户授权服务
            </summary>
        </member>
        <member name="F:Admin.Application.AuthServices.AuthService._oAuth2UserManager">
            <summary>
            oAuth2UserManager
            </summary>
        </member>
        <member name="F:Admin.Application.AuthServices.AuthService._hubContext">
            <summary>
            hubContext
            </summary>
        </member>
        <member name="F:Admin.Application.AuthServices.AuthService._configuration">
            <summary>
            configuration
            </summary>
        </member>
        <member name="F:Admin.Application.AuthServices.AuthService._adminToken">
            <summary>
            IAdminToken
            </summary>
        </member>
        <member name="F:Admin.Application.AuthServices.AuthService._db">
            <summary>
            db
            </summary>
        </member>
        <member name="F:Admin.Application.AuthServices.AuthService._httpContextAccessor">
            <summary>
            IHttpContextAccessor
            </summary>
        </member>
        <member name="F:Admin.Application.AuthServices.AuthService._currentUser">
            <summary>
            当前用户
            </summary>
        </member>
        <member name="M:Admin.Application.AuthServices.AuthService.LoginAsync(Admin.Application.AuthServices.Dtos.LoginInput)">
            <summary>
            用户登录
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.AuthServices.AuthService.GetCallbackAsync(Admin.Application.AuthServices.Dtos.GetCallbackInput)">
            <summary>
            Auht2.0 回调服务
            </summary>
            <param name="input"></param>
        </member>
        <member name="M:Admin.Application.AuthServices.AuthService.BindUserAsync(Admin.Application.AuthServices.Dtos.BindUserInput)">
            <summary>
            绑定用户
            </summary>
            <param name="input">input</param>
        </member>
        <member name="M:Admin.Application.AuthServices.AuthService.RegisterUserAsync(Admin.Application.AuthServices.Dtos.RegisterUserInput)">
            <summary>
            注册用户
            </summary>
            <param name="input">input</param>
        </member>
        <member name="M:Admin.Application.AuthServices.AuthService.GetVbenUserInfoAsync">
            <summary>
            获取当前用户信息（vben）
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.AuthServices.AuthService.GetUserInfoAsync(System.String)">
            <summary>
            获取当前用户信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.AuthServices.AuthService.GetFunctionsAsync">
            <summary>
            获取当前用户的功能
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.AuthServices.AuthService.PutUserInfoAsync(Admin.Application.AuthServices.Dtos.PutUserInfoInput)">
            <summary>
            修改当前用户信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.AuthServices.AuthService.GetOrganizationTreeAsync">
            <summary>
            获取当前用户组织机构树
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.AuthServices.AuthService.GetSystemPlatformInfoAsync">
            <summary>
            获得当前平台信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.AuthServices.AuthService.GetUnreadNoticeAsync">
            <summary>
            获得用户的通知
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.AuthServices.AuthService.GetTokenAndUserInfoAsync(System.Int64)">
            <summary>
            获取TokenAndUserInfo
            </summary>
            <param name="oAuth2UserId"></param>
            <returns></returns>
        </member>
        <member name="P:Admin.Application.AuthServices.Dtos.BindUserInput.ConnectionId">
            <summary>
            ConnectionId
            </summary>
        </member>
        <member name="P:Admin.Application.AuthServices.Dtos.BindUserInput.OAuth2UserId">
            <summary>
            OAuth2User表持久化Id
            </summary>
        </member>
        <member name="P:Admin.Application.AuthServices.Dtos.GetCallbackInput.Code">
            <summary>
            code
            </summary>
        </member>
        <member name="P:Admin.Application.AuthServices.Dtos.GetCallbackInput.State">
            <summary>
            state
            </summary>
        </member>
        <member name="T:Admin.Application.AuthServices.Dtos.GetOrganizationTreeOutput">
            <summary>
            组织机构详情
            </summary>
        </member>
        <member name="P:Admin.Application.AuthServices.Dtos.GetOrganizationTreeOutput.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:Admin.Application.AuthServices.Dtos.GetOrganizationTreeOutput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.Application.AuthServices.Dtos.GetOrganizationTreeOutput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:Admin.Application.AuthServices.Dtos.GetOrganizationTreeOutput.ParentId">
            <summary>
            父级Id
            </summary>
        </member>
        <member name="P:Admin.Application.AuthServices.Dtos.GetOrganizationTreeOutput.Telephone">
            <summary>
            联系电话
            </summary>
        </member>
        <member name="P:Admin.Application.AuthServices.Dtos.GetOrganizationTreeOutput.Leader">
            <summary>
            负责人
            </summary>
        </member>
        <member name="P:Admin.Application.AuthServices.Dtos.GetOrganizationTreeOutput.Sort">
            <summary>
            排序
            </summary>
        </member>
        <member name="P:Admin.Application.AuthServices.Dtos.GetOrganizationTreeOutput.Children">
            <summary>
            子集
            </summary>
        </member>
        <member name="T:Admin.Application.AuthServices.Dtos.GetSystemPlatformInfoOutput">
            <summary>
            系统信息
            </summary>
            <remarks>引用来源https://gitee.com/whuanle/reflection_and_properties/blob/master/%E5%8F%8D%E5%B0%84%E7%89%B9%E6%80%A7%E5%BA%94%E7%94%A8%E5%9C%BA%E6%99%AF1.cs</remarks>
        </member>
        <member name="T:Admin.Application.AuthServices.Dtos.GetUserInfoOutput">
            <summary>
            用户信息输出
            </summary>
        </member>
        <member name="P:Admin.Application.AuthServices.Dtos.GetUserInfoOutput.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:Admin.Application.AuthServices.Dtos.GetUserInfoOutput.Name">
            <summary>
            真实姓名
            </summary>
        </member>
        <member name="P:Admin.Application.AuthServices.Dtos.GetUserInfoOutput.Telephone">
            <summary>
            电话
            </summary>
        </member>
        <member name="P:Admin.Application.AuthServices.Dtos.GetUserInfoOutput.Email">
            <summary>
            邮箱
            </summary>
        </member>
        <member name="T:Admin.Application.AuthServices.Dtos.GetVbenUserInfoOutput">
            <summary>
            用户信息输出
            </summary>
        </member>
        <member name="P:Admin.Application.AuthServices.Dtos.GetVbenUserInfoOutput.UserId">
            <summary>
            用户Id
            </summary>
        </member>
        <member name="P:Admin.Application.AuthServices.Dtos.GetVbenUserInfoOutput.UserName">
            <summary>
            用户名（account）
            </summary>
        </member>
        <member name="P:Admin.Application.AuthServices.Dtos.GetVbenUserInfoOutput.RealName">
            <summary>
            真实姓名
            </summary>
        </member>
        <member name="P:Admin.Application.AuthServices.Dtos.GetVbenUserInfoOutput.Avatar">
            <summary>
            头像
            </summary>
        </member>
        <member name="P:Admin.Application.AuthServices.Dtos.GetVbenUserInfoOutput.Roles">
            <summary>
            角色
            </summary>
        </member>
        <member name="T:Admin.Application.AuthServices.Dtos.LoginInput">
            <summary>
            登录模型
            </summary>
        </member>
        <member name="P:Admin.Application.AuthServices.Dtos.LoginInput.Account">
            <summary>
            账号
            </summary>
        </member>
        <member name="P:Admin.Application.AuthServices.Dtos.LoginInput.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="T:Admin.Application.AuthServices.Dtos.LoginOutput">
            <summary>
            登录模型
            </summary>
        </member>
        <member name="P:Admin.Application.AuthServices.Dtos.LoginOutput.Id">
            <summary>
            用户Id
            </summary>
        </member>
        <member name="P:Admin.Application.AuthServices.Dtos.LoginOutput.Name">
            <summary>
            姓名
            </summary>
        </member>
        <member name="T:Admin.Application.AuthServices.Dtos.PutUserInfoInput">
            <summary>
            用户信息输出
            </summary>
        </member>
        <member name="P:Admin.Application.AuthServices.Dtos.PutUserInfoInput.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:Admin.Application.AuthServices.Dtos.PutUserInfoInput.Name">
            <summary>
            真实姓名
            </summary>
        </member>
        <member name="P:Admin.Application.AuthServices.Dtos.PutUserInfoInput.Telephone">
            <summary>
            电话
            </summary>
        </member>
        <member name="P:Admin.Application.AuthServices.Dtos.PutUserInfoInput.Email">
            <summary>
            邮箱
            </summary>
        </member>
        <member name="T:Admin.Application.AuthServices.Dtos.RegisterUserInput">
            <summary>
            用户注册模型
            </summary>
        </member>
        <member name="P:Admin.Application.AuthServices.Dtos.RegisterUserInput.OAuth2UserId">
            <summary>
            OAuth2User表持久化Id
            </summary>
        </member>
        <member name="P:Admin.Application.AuthServices.Dtos.RegisterUserInput.ConnectionId">
            <summary>
            ConnectionId
            </summary>
        </member>
        <member name="P:Admin.Application.AuthServices.Dtos.RegisterUserInput.Name">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:Admin.Application.AuthServices.Dtos.RegisterUserInput.Telephone">
            <summary>
            电话
            </summary>
        </member>
        <member name="P:Admin.Application.AuthServices.Dtos.RegisterUserInput.Email">
            <summary>
            邮箱
            </summary>
        </member>
        <member name="T:Admin.Application.AuthServices.Dtos.RegisterUserInputToEntity">
            <summary>
            注册用户映射
            </summary>
        </member>
        <member name="T:Admin.Application.DeviceServices.DeviceDataService">
            <summary>
            设备数据服务
            </summary>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceDataService.#ctor(SqlSugar.ISqlSugarClient)">
            <summary>
            设备数据服务
            </summary>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceDataService.GetHistoryDataAsync(Admin.Application.DeviceServices.Dto.GetDeviceHistoryInput)">
            <summary>
            获取设备历史数据
            </summary>
            <param name="input">查询输入</param>
            <returns>分页的历史数据</returns>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceDataService.IsDeviceOnlineAsync(System.Int64,System.Int32)">
            <summary>
            检查设备是否在线
            </summary>
            <param name="deviceId">设备ID</param>
            <param name="timeoutMinutes">超时时间（分钟）</param>
            <returns>是否在线</returns>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceDataService.GetOfflineDevicesAsync(System.Int32)">
            <summary>
            获取离线设备列表
            </summary>
            <param name="timeoutMinutes">超时时间（分钟）</param>
            <returns>离线设备列表</returns>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceDataService.DeleteHistoryDataAsync(System.Int64,System.DateTime)">
            <summary>
            删除设备历史数据
            </summary>
            <param name="deviceId">设备ID</param>
            <param name="beforeDate">删除此时间之前的数据</param>
            <returns>删除的记录数</returns>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceDataService.BatchDeleteHistoryDataAsync(System.DateTime)">
            <summary>
            批量删除历史数据
            </summary>
            <param name="beforeDate">删除此时间之前的数据</param>
            <returns>删除的记录数</returns>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceDataService.DeleteDeviceLatestDataAsync(System.Int64)">
            <summary>
            删除设备所有最新数据
            </summary>
            <param name="deviceId">设备ID</param>
            <returns>删除的记录数</returns>
        </member>
        <member name="T:Admin.Application.DeviceServices.DeviceInstructionService">
            <summary>
            设备指令服务
            </summary>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceInstructionService.#ctor(SqlSugar.ISqlSugarClient,Admin.SqlSugar.Repository{Admin.SqlSugar.Entity.Business.LOT.DeviceInstructionEntity},Microsoft.Extensions.Logging.ILogger{Admin.Application.DeviceServices.DeviceInstructionService})">
            <summary>
            设备指令服务
            </summary>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceInstructionService.GetPagedListAsync(Admin.Application.DeviceServices.Dto.DeviceInstructionQueryInput)">
            <summary>
            分页查询设备指令
            </summary>
            <param name="input">查询条件</param>
            <returns>分页结果</returns>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceInstructionService.GetByIdAsync(System.Int64)">
            <summary>
            根据ID获取设备指令
            </summary>
            <param name="id">指令ID</param>
            <returns>设备指令信息</returns>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceInstructionService.GetByDeviceIdAsync(System.Int64)">
            <summary>
            根据设备ID获取指令列表
            </summary>
            <param name="deviceId">设备ID</param>
            <returns>指令列表</returns>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceInstructionService.AddAsync(Admin.Application.DeviceServices.Dto.AddDeviceInstructionInput)">
            <summary>
            添加设备指令
            </summary>
            <param name="input">指令信息</param>
            <returns>指令信息</returns>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceInstructionService.UpdateAsync(System.Int64,Admin.Application.DeviceServices.Dto.UpdateDeviceInstructionInput)">
            <summary>
            更新设备指令
            </summary>
            <param name="id">指令ID</param>
            <param name="input">更新信息</param>
            <returns>更新后的指令信息</returns>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceInstructionService.DeleteAsync(System.Int64)">
            <summary>
            删除设备指令
            </summary>
            <param name="id">指令ID</param>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceInstructionService.BatchDeleteAsync(System.Collections.Generic.List{System.Int64})">
            <summary>
            批量删除设备指令
            </summary>
            <param name="ids">指令ID列表</param>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceInstructionService.SetEnabledAsync(System.Int64,System.Boolean)">
            <summary>
            启用/禁用设备指令
            </summary>
            <param name="id">指令ID</param>
            <param name="isEnabled">是否启用</param>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceInstructionService.GetSimpleListAsync(System.Int64,System.Boolean)">
            <summary>
            获取设备指令简单列表（用于下拉选择等场景）
            </summary>
            <param name="deviceId">设备ID</param>
            <param name="enabledOnly">是否只返回启用的指令</param>
            <returns>简单指令列表</returns>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceInstructionService.MapToOutput(Admin.SqlSugar.Entity.Business.LOT.DeviceInstructionEntity)">
            <summary>
            映射实体到输出DTO
            </summary>
            <param name="entity">设备指令实体</param>
            <returns>设备指令输出DTO</returns>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceInstructionService.ValidateDeviceExistsAsync(System.Int64)">
            <summary>
            验证设备是否存在
            </summary>
            <param name="deviceId">设备ID</param>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceInstructionService.ValidateInstructionNameUniqueAsync(System.Int64,System.String,System.Nullable{System.Int64})">
            <summary>
            验证指令名称是否唯一
            </summary>
            <param name="deviceId">设备ID</param>
            <param name="instructionName">指令名称</param>
            <param name="excludeId">排除的指令ID（用于更新时验证）</param>
        </member>
        <member name="T:Admin.Application.DeviceServices.DeviceParaService">
            <summary>
            设备参数管理服务
            </summary>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceParaService.#ctor(SqlSugar.ISqlSugarClient,Admin.SqlSugar.Repository{Admin.SqlSugar.Entity.Business.LOT.DeviceParaEntity},Microsoft.Extensions.Logging.ILogger{Admin.Application.DeviceServices.DeviceParaService})">
            <summary>
            设备参数管理服务
            </summary>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceParaService.QueryAsync(Admin.Application.DeviceServices.Dto.DeviceParaQueryInput)">
            <summary>
            分页查询设备参数
            </summary>
            <param name="input">查询条件</param>
            <returns>设备参数分页列表</returns>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceParaService.GetByIdAsync(System.Int64)">
            <summary>
            根据ID获取设备参数
            </summary>
            <param name="parameterId">参数ID</param>
            <returns>设备参数信息</returns>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceParaService.GetByDeviceIdAsync(System.Int64)">
            <summary>
            根据设备ID获取参数列表
            </summary>
            <param name="deviceId">设备ID</param>
            <returns>设备参数列表</returns>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceParaService.GetSimpleListByDeviceIdAsync(System.Int64)">
            <summary>
            根据设备ID获取简单参数列表（用于下拉选择）
            </summary>
            <param name="deviceId">设备ID</param>
            <returns>设备参数简单列表</returns>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceParaService.AddAsync(Admin.Application.DeviceServices.Dto.AddDeviceParaInput)">
            <summary>
            添加设备参数
            </summary>
            <param name="input">设备参数信息</param>
            <returns>设备参数信息</returns>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceParaService.UpdateAsync(Admin.Application.DeviceServices.Dto.UpdateDeviceParaInput)">
            <summary>
            更新设备参数
            </summary>
            <param name="input">设备参数信息</param>
            <returns>设备参数信息</returns>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceParaService.DeleteAsync(System.Int64)">
            <summary>
            删除设备参数
            </summary>
            <param name="parameterId">参数ID</param>
            <returns>删除结果</returns>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceParaService.BatchDeleteAsync(Admin.Application.DeviceServices.Dto.BatchDeleteDeviceParaInput)">
            <summary>
            批量删除设备参数
            </summary>
            <param name="input">批量删除输入</param>
            <returns>删除结果</returns>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceParaService.SetMonitorStatusAsync(Admin.Application.DeviceServices.Dto.SetDeviceParaMonitorStatusInput)">
            <summary>
            设置设备参数监控状态
            </summary>
            <param name="input">设置监控状态输入</param>
            <returns>设置结果</returns>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceParaService.ValidateDeviceExistsAsync(System.Int64)">
            <summary>
            验证设备是否存在
            </summary>
            <param name="deviceId">设备ID</param>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceParaService.ValidateParameterKeyUniqueAsync(System.Int64,System.String,System.Nullable{System.Int64})">
            <summary>
            验证参数标识符在同一设备下的唯一性
            </summary>
            <param name="deviceId">设备ID</param>
            <param name="key">参数标识符</param>
            <param name="excludeParameterId">排除的参数ID（用于更新时排除自身）</param>
        </member>
        <member name="T:Admin.Application.DeviceServices.DeviceService">
            <summary>
            设备管理服务
            </summary>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceService.#ctor(SqlSugar.ISqlSugarClient,Admin.SqlSugar.Repository{Admin.SqlSugar.Entity.Business.LOT.DeviceEntity},Admin.Communication.Mqtt.Abstractions.IMqttUserService,Admin.Application.MqttBrokerServices.IMqttAclManagementService,Admin.Communication.Alarm.Services.AlarmEventGeneratorService,Microsoft.Extensions.Logging.ILogger{Admin.Application.DeviceServices.DeviceService})">
            <summary>
            设备管理服务
            </summary>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceService.AddAsync(Admin.Application.DeviceServices.Dto.AddDeviceInput)">
            <summary>
            添加设备
            </summary>
            设备添加流程：
            选择模型 -> 创建设备 -> 如果协议类型为modbus、数据格式为HEX时同步模型指令至设备指令 -> 同步模型参数到设备参数表
            直连设备和网关设备创建ACL规则并创建创建mqtt用户为DeviceId，密码为主键Id
            <param name="input">设备信息</param>
            <returns>设备实体信息</returns>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceService.GetByIdAsync(System.Int64)">
            <summary>
            根据ID获取设备
            </summary>
            <param name="id">设备ID</param>
            <returns>设备信息</returns>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceService.QueryAsync(Admin.Application.DeviceServices.Dto.DeviceQueryInput)">
            <summary>
            分页查询设备
            </summary>
            <param name="input">查询条件</param>
            <returns>设备分页列表</returns>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceService.UpdateAsync(System.Int64,Admin.Application.DeviceServices.Dto.UpdateDeviceInput)">
            <summary>
            更新设备
            </summary>
            <param name="id">设备ID</param>
            <param name="input">更新信息</param>
            <returns>是否成功</returns>
            只能修改DeviceName、DeviceDescription、IpAddress
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceService.DeleteAsync(System.Int64)">
            <summary>
            删除设备
            </summary>
            <param name="id">设备ID</param>
            <returns>是否成功</returns>
            删除设备包含：设备参数表 - 设备指令表(如有) - 设备MQTT用户 - ACL规则 ->设备主题配置表
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceService.EnableAsync(System.Int64)">
            <summary>
            启用设备
            </summary>
            <param name="id">设备ID</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceService.DisableAsync(System.Int64)">
            <summary>
            禁用设备
            </summary>
            <param name="id">设备ID</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceService.ResetSecretAsync(System.Int64)">
            <summary>
            重置设备密钥
            </summary>
            <param name="id">设备ID</param>
            <returns>新的设备密钥</returns>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceService.CreateMqttAclRulesAsync(Admin.SqlSugar.Entity.Business.LOT.DeviceEntity)">
            <summary>
            创建MQTT ACL规则
            </summary>
            <param name="entity">设备实体</param>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceService.DeleteDeviceRelatedDataAsync(System.Int64)">
            <summary>
            删除设备相关数据（设备参数表、设备指令表、设备主题配置表）
            </summary>
            <param name="deviceId">设备ID</param>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceService.DeleteDeviceMqttUsersAsync(Admin.SqlSugar.Entity.Business.LOT.DeviceEntity)">
            <summary>
            删除设备对应的MQTT用户和ACL规则
            </summary>
            <param name="device">设备实体</param>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceService.ValidateDeviceInputAsync(Admin.Application.DeviceServices.Dto.AddDeviceInput,Admin.SqlSugar.Entity.Business.LOT.ProductEntity)">
            <summary>
            验证设备输入数据
            </summary>
            <param name="input">设备输入数据</param>
            <param name="product">产品信息</param>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceService.CreateDeviceEntity(Admin.Application.DeviceServices.Dto.AddDeviceInput,Admin.SqlSugar.Entity.Business.LOT.ProductEntity)">
            <summary>
            创建设备实体
            </summary>
            <param name="input">设备输入数据</param>
            <param name="product">产品信息</param>
            <returns>设备实体</returns>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceService.GenerateDeviceId(System.String,System.String)">
            <summary>
            生成设备ID
            </summary>
            <param name="providedDeviceId">用户提供的设备ID</param>
            <param name="deviceIdentityCode">设备标识码</param>
            <returns>生成的设备ID</returns>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceService.SyncModelParametersToDeviceAsync(System.Int64,System.Int64,Admin.SqlSugar.Entity.Business.LOT.ProductEntity)">
            <summary>
            同步模型参数到设备参数表
            </summary>
            <param name="deviceId">设备ID</param>
            <param name="modelId">模型ID</param>
            <param name="product">产品信息</param>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceService.SyncMqttModelPropertiesToDeviceAsync(System.Int64,System.Int64)">
            <summary>
            同步MQTT模型属性到设备参数表
            </summary>
            <param name="deviceId">设备ID</param>
            <param name="modelId">模型ID</param>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceService.SyncModbusModelInstructionsToDeviceAsync(System.Int64,System.Int64)">
            <summary>
            同步Modbus模型指令到设备指令表和设备参数表
            </summary>
            <param name="deviceId">设备ID</param>
            <param name="modelId">模型ID</param>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceService.GenerateModbusRtuCommandAsync(System.Int64,Admin.SqlSugar.Entity.Business.LOT.ModelInstructionEntity)">
            <summary>
            生成Modbus RTU指令
            指令组成：ModbusAddr + FunctionCode + StartAddress + ReadCount + CRC16
            </summary>
            <param name="deviceId">设备ID</param>
            <param name="instruction">模型指令</param>
            <returns>16进制格式的Modbus RTU指令</returns>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceService.GetProductByModelIdAsync(System.Int64)">
            <summary>
            通过ModelId获取产品信息
            </summary>
            <param name="modelId">模型ID</param>
            <returns>产品信息</returns>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceService.GetDeviceTypeAsync(System.Int64)">
            <summary>
            获取设备类型（通过模型关联的产品信息）
            </summary>
            <param name="modelId">模型ID</param>
            <returns>设备类型</returns>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceService.BuildDeviceOutput(Admin.SqlSugar.Entity.Business.LOT.DeviceEntity,Admin.Application.DeviceServices.Dto.AddDeviceInput,Admin.SqlSugar.Entity.Business.LOT.ProductEntity)">
            <summary>
            构建设备输出DTO
            </summary>
            <param name="entity">设备实体</param>
            <param name="input">输入参数</param>
            <param name="product">产品信息</param>
            <returns>设备输出DTO</returns>
        </member>
        <member name="T:Admin.Application.DeviceServices.DeviceTopicConfigService">
            <summary>
            设备主题配置服务
            </summary>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceTopicConfigService.#ctor(SqlSugar.ISqlSugarClient,Admin.SqlSugar.Repository{Admin.SqlSugar.Entity.Business.LOT.DeviceTopicConfigEntity},Admin.Application.MqttBrokerServices.IMqttAclManagementService,Microsoft.Extensions.Logging.ILogger{Admin.Application.DeviceServices.DeviceTopicConfigService})">
            <summary>
            设备主题配置服务
            </summary>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceTopicConfigService.GetPagedListAsync(Admin.Application.DeviceServices.Dto.DeviceTopicConfigQueryInput)">
            <summary>
            分页查询设备主题配置
            </summary>
            <param name="input">查询条件</param>
            <returns>分页结果</returns>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceTopicConfigService.GetByIdAsync(System.Int64)">
            <summary>
            根据ID获取设备主题配置
            </summary>
            <param name="id">配置ID</param>
            <returns>设备主题配置信息</returns>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceTopicConfigService.GetByDeviceIdAsync(System.String)">
            <summary>
            根据设备ID获取主题配置列表
            </summary>
            <param name="deviceId">设备ID</param>
            <returns>主题配置列表</returns>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceTopicConfigService.AddAsync(Admin.Application.DeviceServices.Dto.AddDeviceTopicConfigInput)">
            <summary>
            添加设备主题配置
            </summary>
            <param name="input">配置信息</param>
            <returns>配置实体信息</returns>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceTopicConfigService.DeleteAsync(System.Int64)">
            <summary>
            删除设备主题配置
            </summary>
            <param name="id">配置ID</param>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceTopicConfigService.BatchDeleteAsync(System.Collections.Generic.List{System.Int64})">
            <summary>
            批量删除设备主题配置
            </summary>
            <param name="ids">配置ID列表</param>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceTopicConfigService.DeleteByDeviceIdAsync(System.String)">
            <summary>
            根据设备ID删除所有主题配置
            </summary>
            <param name="deviceId">设备ID</param>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceTopicConfigService.ValidateDeviceExistsAsync(System.String)">
            <summary>
            验证设备是否存在
            </summary>
            <param name="deviceId">设备ID</param>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceTopicConfigService.ValidateTopicFormat(System.String)">
            <summary>
            验证主题格式
            </summary>
            <param name="topic">主题</param>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceTopicConfigService.ValidateTemplateNameUniqueAsync(System.String,System.String,System.Nullable{System.Int64})">
            <summary>
            验证模板名称唯一性
            </summary>
            <param name="deviceId">设备ID</param>
            <param name="templateName">模板名称</param>
            <param name="excludeId">排除的配置ID（用于更新时）</param>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceTopicConfigService.ValidateTopicUniqueAsync(System.String,System.String,System.Nullable{System.Int64})">
            <summary>
            验证主题唯一性
            </summary>
            <param name="deviceId">设备ID</param>
            <param name="topic">主题</param>
            <param name="excludeId">排除的配置ID（用于更新时）</param>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceTopicConfigService.UpdateDeviceCustomPublishTopicAsync(System.String,System.String)">
            <summary>
            更新设备的CustomPublishTopic属性
            </summary>
            <param name="deviceId">设备ID</param>
            <param name="topic">主题</param>
        </member>
        <member name="M:Admin.Application.DeviceServices.DeviceTopicConfigService.CreateTopicAclRuleAsync(System.String,System.String,System.Int32)">
            <summary>
            为主题配置创建对应的MQTT ACL规则
            </summary>
            <param name="deviceId">设备ID</param>
            <param name="topic">主题</param>
            <param name="accessType">访问类型(0=All, 1=Publish, 2=Subscribe)</param>
        </member>
        <member name="T:Admin.Application.DeviceServices.Dto.DeviceDataLatestOutput">
            <summary>
            设备最新数据输出
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceDataLatestOutput.Id">
            <summary>
            主键ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceDataLatestOutput.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceDataLatestOutput.DeviceName">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceDataLatestOutput.DeviceCode">
            <summary>
            设备编码
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceDataLatestOutput.ProductId">
            <summary>
            产品ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceDataLatestOutput.ProductName">
            <summary>
            产品名称
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceDataLatestOutput.ModelId">
            <summary>
            模型ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceDataLatestOutput.ModelName">
            <summary>
            模型名称
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceDataLatestOutput.PropertyId">
            <summary>
            属性ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceDataLatestOutput.PropertyName">
            <summary>
            属性名称
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceDataLatestOutput.DataType">
            <summary>
            数据类型（1:int整形 2:long长整型 3:decimal小数 4:string字符串 5:datetime日期时间 6:JSON结构体 7:enum枚举 8:boolean布尔 9:stringList数组）
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceDataLatestOutput.DataValue">
            <summary>
            数据值
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceDataLatestOutput.Unit">
            <summary>
            属性单位
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceDataLatestOutput.DataStatus">
            <summary>
            数据状态 (1:正常 2:异常)
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceDataLatestOutput.UpdateTime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceDataLatestOutput.DeviceStatus">
            <summary>
            设备状态 (0:离线 1:在线)
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceDataLatestOutput.IsOnline">
            <summary>
            是否在线
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceDataLatestOutput.OfflineMinutes">
            <summary>
            离线时长（分钟）
            </summary>
        </member>
        <member name="T:Admin.Application.DeviceServices.Dto.DeviceDataHistoryOutput">
            <summary>
            设备历史数据输出
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceDataHistoryOutput.Id">
            <summary>
            主键ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceDataHistoryOutput.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceDataHistoryOutput.DeviceName">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceDataHistoryOutput.PropertyName">
            <summary>
            属性名称
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceDataHistoryOutput.PropertyValue">
            <summary>
            属性值
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceDataHistoryOutput.DataTime">
            <summary>
            数据时间戳
            </summary>
        </member>
        <member name="T:Admin.Application.DeviceServices.Dto.GetDeviceHistoryInput">
            <summary>
            获取设备历史数据输入
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.GetDeviceHistoryInput.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.GetDeviceHistoryInput.StartTime">
            <summary>
            开始时间
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.GetDeviceHistoryInput.EndTime">
            <summary>
            结束时间
            </summary>
        </member>
        <member name="T:Admin.Application.DeviceServices.Dto.DeviceDataStatisticsOutput">
            <summary>
            设备数据统计输出
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceDataStatisticsOutput.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceDataStatisticsOutput.TotalCount">
            <summary>
            总数据量
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceDataStatisticsOutput.EarliestTime">
            <summary>
            最早数据时间
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceDataStatisticsOutput.LatestTime">
            <summary>
            最新数据时间
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceDataStatisticsOutput.TimeRange">
            <summary>
            统计时间范围
            </summary>
        </member>
        <member name="T:Admin.Application.DeviceServices.Dto.GetDataStatisticsInput">
            <summary>
            获取设备数据统计输入
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.GetDataStatisticsInput.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.GetDataStatisticsInput.StartTime">
            <summary>
            开始时间
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.GetDataStatisticsInput.EndTime">
            <summary>
            结束时间
            </summary>
        </member>
        <member name="T:Admin.Application.DeviceServices.Dto.DeviceStatusOverviewOutput">
            <summary>
            设备状态概览输出
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceStatusOverviewOutput.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceStatusOverviewOutput.DeviceName">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceStatusOverviewOutput.DeviceCode">
            <summary>
            设备编码
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceStatusOverviewOutput.DeviceStatus">
            <summary>
            设备状态 (0:离线 1:在线)
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceStatusOverviewOutput.LastDataTime">
            <summary>
            最后数据时间
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceStatusOverviewOutput.IsOnline">
            <summary>
            是否在线
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceStatusOverviewOutput.OfflineMinutes">
            <summary>
            离线时长（分钟）
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceStatusOverviewOutput.DataJson">
            <summary>
            设备数据JSON
            </summary>
        </member>
        <member name="T:Admin.Application.DeviceServices.Dto.BatchGetLatestDataInput">
            <summary>
            批量获取设备最新数据输入
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.BatchGetLatestDataInput.DeviceIds">
            <summary>
            设备ID列表
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.BatchGetLatestDataInput.IncludeOffline">
            <summary>
            是否包含离线设备
            </summary>
        </member>
        <member name="T:Admin.Application.DeviceServices.Dto.DeviceDataTrendOutput">
            <summary>
            设备数据趋势输出
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceDataTrendOutput.TimeGroup">
            <summary>
            时间分组
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceDataTrendOutput.DataCount">
            <summary>
            数据数量
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceDataTrendOutput.AvgValue">
            <summary>
            平均值
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceDataTrendOutput.MaxValue">
            <summary>
            最大值
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceDataTrendOutput.MinValue">
            <summary>
            最小值
            </summary>
        </member>
        <member name="T:Admin.Application.DeviceServices.Dto.SaveDeviceLatestDataInput">
            <summary>
            保存设备最新数据输入
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.SaveDeviceLatestDataInput.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.SaveDeviceLatestDataInput.ModelId">
            <summary>
            模型ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.SaveDeviceLatestDataInput.PropertyId">
            <summary>
            属性ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.SaveDeviceLatestDataInput.PropertyName">
            <summary>
            属性名称
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.SaveDeviceLatestDataInput.DataType">
            <summary>
            数据类型（1:int整形 2:long长整型 3:decimal小数 4:string字符串 5:datetime日期时间 6:JSON结构体 7:enum枚举 8:boolean布尔 9:stringList数组）
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.SaveDeviceLatestDataInput.DataValue">
            <summary>
            数据值
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.SaveDeviceLatestDataInput.Unit">
            <summary>
            属性单位
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.SaveDeviceLatestDataInput.DataStatus">
            <summary>
            数据状态 (1:正常 2:异常)
            </summary>
        </member>
        <member name="T:Admin.Application.DeviceServices.Dto.GetLatestPropertyDataInput">
            <summary>
            获取设备属性最新数据输入
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.GetLatestPropertyDataInput.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.GetLatestPropertyDataInput.PropertyId">
            <summary>
            属性ID
            </summary>
        </member>
        <member name="T:Admin.Application.DeviceServices.Dto.GetDataTrendInput">
            <summary>
            获取设备数据趋势输入
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.GetDataTrendInput.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.GetDataTrendInput.StartTime">
            <summary>
            开始时间
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.GetDataTrendInput.EndTime">
            <summary>
            结束时间
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.GetDataTrendInput.TimeFormat">
            <summary>
            时间格式化字符串
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.GetDataTrendInput.PropertyPath">
            <summary>
            属性路径（JSON路径）
            </summary>
        </member>
        <member name="T:Admin.Application.DeviceServices.Dto.DeviceQueryInput">
            <summary>
            设备查询输入DTO
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceQueryInput.DeviceName">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceQueryInput.Status">
            <summary>
            设备状态 (0:离线 1:在线)
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceQueryInput.ProductId">
            <summary>
            产品ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceQueryInput.ParentId">
            <summary>
            父设备ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceQueryInput.GroupKey">
            <summary>
            分组标识
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceQueryInput.DeviceIdentityCode">
            <summary>
            设备标识码
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceQueryInput.ProtocolType">
            <summary>
            设备协议类型
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceQueryInput.DataFormat">
            <summary>
            设备数据格式
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceQueryInput.DeviceType">
            <summary>
            设备属性类型
            </summary>
        </member>
        <member name="T:Admin.Application.DeviceServices.Dto.AddDeviceInput">
            <summary>
            添加设备输入DTO
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceInput.ModelId">
            <summary>
            模型ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceInput.ParentId">
            <summary>
            父设备ID (0表示无父设备)
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceInput.GroupKey">
            <summary>
            分组标识 (网关类型产品使用，对应JSON中的分组key，如"IO"、"wenshidu")
            直连设备此字段为空
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceInput.DeviceId">
            <summary>
            设备ID，用于唯一标识一个设备。如果填写该参数，平台将设备ID设置为该参数值；
            设备ID长度为4至128个字符，只允许字母、数字、下划线（_）、连接符（-）的组合。
            如果不填写该参数，设备ID由物联网平台分配获得，生成规则为guid + _ + IdentityCode拼接而成。
            网关子设备为空字符串
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceInput.DeviceIdentityCode">
            <summary>
            设备标识码 (通常使用IMEI、MAC地址或Serial No作为设备标识码)
            设备标识码长度为4至64个字符，只允许字母、数字、下划线（_）、连接符（-）的组合
            网关子设备为空字符串
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceInput.DeviceName">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceInput.DeviceDescription">
            <summary>
            设备描述
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceInput.IpAddress">
            <summary>
            设备IP地址
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceInput.ModbusAddr">
            <summary>
            设备串口地址 (Modbus设备使用)
            </summary>
        </member>
        <member name="T:Admin.Application.DeviceServices.Dto.UpdateDeviceInput">
            <summary>
            更新设备输入DTO
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceInput.ProductId">
            <summary>
            产品ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceInput.ModelId">
            <summary>
            模型ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceInput.ProtocolType">
            <summary>
            设备协议 (1:MQTT 2:Modbus)
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceInput.DataFormat">
            <summary>
            设备数据格式 (1:JSON 2:HEX)
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceInput.DeviceType">
            <summary>
            设备属性 (1:直连设备 2:网关设备 3:网关子设备)
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceInput.ParentId">
            <summary>
            父设备ID (0表示无父设备)
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceInput.GroupKey">
            <summary>
            分组标识 (网关类型产品使用，对应JSON中的分组key，如"IO"、"wenshidu")
            直连设备此字段为空
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceInput.DeviceId">
            <summary>
            设备ID，用于唯一标识一个设备。
            设备ID长度为4至128个字符，只允许字母、数字、下划线（_）、连接符（-）的组合。
            网关子设备为空字符串
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceInput.DeviceIdentityCode">
            <summary>
            设备标识码 (通常使用IMEI、MAC地址或Serial No作为设备标识码)
            设备标识码长度为4至64个字符，只允许字母、数字、下划线（_）、连接符（-）的组合
            网关子设备为空字符串
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceInput.DeviceName">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceInput.DeviceDescription">
            <summary>
            设备描述
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceInput.IpAddress">
            <summary>
            设备IP地址
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceInput.ModbusAddr">
            <summary>
            设备串口地址 (Modbus设备使用)
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceInput.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="T:Admin.Application.DeviceServices.Dto.DeviceOutput">
            <summary>
            设备输出DTO
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceOutput.Id">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceOutput.ProductId">
            <summary>
            产品ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceOutput.ModelId">
            <summary>
            模型ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceOutput.ProtocolType">
            <summary>
            设备协议 (1:MQTT 2:Modbus)
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceOutput.DataFormat">
            <summary>
            设备数据格式 (1:JSON 2:二进制码流)
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceOutput.DeviceType">
            <summary>
            设备属性 (1:直连设备 2:网关设备 3:网关子设备)
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceOutput.ParentId">
            <summary>
            父设备ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceOutput.GroupKey">
            <summary>
            分组标识
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceOutput.DeviceId">
            <summary>
            设备ID，用于唯一标识一个设备
            网关子设备为空字符串
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceOutput.DeviceIdentityCode">
            <summary>
            设备标识码 (网关子设备为空字符串)
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceOutput.DeviceName">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceOutput.DeviceDescription">
            <summary>
            设备描述
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceOutput.Status">
            <summary>
            设备状态 (0:离线 1:在线)
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceOutput.IpAddress">
            <summary>
            设备IP地址
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceOutput.ModbusAddr">
            <summary>
            设备串口地址 (Modbus设备使用)
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceOutput.DeviceSecret">
            <summary>
            设备密钥 (网关子设备为null)
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceOutput.LastDataTime">
            <summary>
            最后数据上报时间
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceOutput.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceOutput.CreateBy">
            <summary>
            创建人
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceOutput.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceOutput.UpdateBy">
            <summary>
            修改人
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceOutput.UpdateTime">
            <summary>
            修改时间
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceOutput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Admin.Application.DeviceServices.Dto.DeviceInstructionQueryInput">
            <summary>
            设备指令查询输入DTO
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceInstructionQueryInput.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceInstructionQueryInput.InstructionName">
            <summary>
            指令名称
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceInstructionQueryInput.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="T:Admin.Application.DeviceServices.Dto.AddDeviceInstructionInput">
            <summary>
            添加设备指令输入DTO
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceInstructionInput.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceInstructionInput.InstructionName">
            <summary>
            指令名称
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceInstructionInput.SendStr">
            <summary>
            发送指令 (例如 010300000002C40B)
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceInstructionInput.Encode">
            <summary>
            编码 (1:HEX 2:ASCII)
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceInstructionInput.ResponseTime">
            <summary>
            响应时间(毫秒)
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceInstructionInput.RetryCount">
            <summary>
            重试次数
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceInstructionInput.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceInstructionInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Admin.Application.DeviceServices.Dto.UpdateDeviceInstructionInput">
            <summary>
            更新设备指令输入DTO
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceInstructionInput.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceInstructionInput.InstructionName">
            <summary>
            指令名称
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceInstructionInput.SendStr">
            <summary>
            发送指令 (例如 010300000002C40B)
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceInstructionInput.Encode">
            <summary>
            编码 (1:HEX 2:ASCII)
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceInstructionInput.ResponseTime">
            <summary>
            响应时间(毫秒)
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceInstructionInput.RetryCount">
            <summary>
            重试次数
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceInstructionInput.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceInstructionInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Admin.Application.DeviceServices.Dto.DeviceInstructionOutput">
            <summary>
            设备指令输出DTO
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceInstructionOutput.Id">
            <summary>
            指令ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceInstructionOutput.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceInstructionOutput.InstructionName">
            <summary>
            指令名称
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceInstructionOutput.SendStr">
            <summary>
            发送指令
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceInstructionOutput.Encode">
            <summary>
            编码 (1:HEX 2:ASCII)
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceInstructionOutput.ResponseTime">
            <summary>
            响应时间(毫秒)
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceInstructionOutput.RetryCount">
            <summary>
            重试次数
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceInstructionOutput.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceInstructionOutput.CreateBy">
            <summary>
            创建人
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceInstructionOutput.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceInstructionOutput.UpdateBy">
            <summary>
            修改人
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceInstructionOutput.UpdateTime">
            <summary>
            修改时间
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceInstructionOutput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Admin.Application.DeviceServices.Dto.DeviceInstructionSimpleOutput">
            <summary>
            设备指令简单输出DTO (用于下拉选择等场景)
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceInstructionSimpleOutput.Id">
            <summary>
            指令ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceInstructionSimpleOutput.InstructionName">
            <summary>
            指令名称
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceInstructionSimpleOutput.SendStr">
            <summary>
            发送指令
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceInstructionSimpleOutput.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="T:Admin.Application.DeviceServices.Dto.DeviceParaQueryInput">
            <summary>
            设备参数查询输入DTO
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceParaQueryInput.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceParaQueryInput.Name">
            <summary>
            参数名称
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceParaQueryInput.DataType">
            <summary>
            数据类型（1:decimal 模拟量 2:string字符串 3:datetime时间 4:json 5:enum枚举）
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceParaQueryInput.MonitorStatus">
            <summary>
            监控状态（0: 不启用 1：启用）
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceParaQueryInput.IsSave">
            <summary>
            是否保存
            </summary>
        </member>
        <member name="T:Admin.Application.DeviceServices.Dto.AddDeviceParaInput">
            <summary>
            添加设备参数输入DTO
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceParaInput.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceParaInput.InstructionId">
            <summary>
            指令ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceParaInput.Name">
            <summary>
            参数名称
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceParaInput.Sort">
            <summary>
            参数序号
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceParaInput.Key">
            <summary>
            参数标识符 (JSON中的路径，支持多层嵌套，如: "IO.input.channel1" 或 "sensors.temperature")
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceParaInput.DataType">
            <summary>
            数据类型（1:decimal 模拟量 2:string字符串 3:datetime时间 4:json 5:enum枚举）
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceParaInput.EnumDescription">
            <summary>
            枚举说明
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceParaInput.Unit">
            <summary>
            单位
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceParaInput.DivisionFactor">
            <summary>
            倍率
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceParaInput.DecimalPlaces">
            <summary>
            小数位数
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceParaInput.CorrectionScale">
            <summary>
            校正比例（默认为1）
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceParaInput.CorrectionAmplitude">
            <summary>
            校正幅度（默认为0）
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceParaInput.AlarmUpperLimit">
            <summary>
            报警上限
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceParaInput.AlarmUpperLimitClearValue">
            <summary>
            报警上限解除值
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceParaInput.AlarmLowerLimit">
            <summary>
            报警下限
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceParaInput.AlarmLowerLimitClearValue">
            <summary>
            报警下限解除值
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceParaInput.WarningUpperLimit">
            <summary>
            预警上限
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceParaInput.WarningUpperLimitClearValue">
            <summary>
            预警上限解除值
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceParaInput.WarningLowerLimit">
            <summary>
            预警下限
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceParaInput.WarningLowerLimitClearValue">
            <summary>
            预警下限解除值
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceParaInput.IsSave">
            <summary>
            是否保存
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceParaInput.SaveAmplitude">
            <summary>
            保存幅度
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceParaInput.SaveAmplitudeType">
            <summary>
            保存幅度类型（0: 数值 1：百分比）
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceParaInput.SaveInterval">
            <summary>
            保存间隔(毫秒)
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceParaInput.MonitorStatus">
            <summary>
            监控状态（0: 不启用 1：启用）
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceParaInput.Description">
            <summary>
            参数描述
            </summary>
        </member>
        <member name="T:Admin.Application.DeviceServices.Dto.UpdateDeviceParaInput">
            <summary>
            更新设备参数输入DTO
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceParaInput.ParameterId">
            <summary>
            参数ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceParaInput.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceParaInput.InstructionId">
            <summary>
            指令ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceParaInput.Name">
            <summary>
            参数名称
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceParaInput.Sort">
            <summary>
            参数序号
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceParaInput.Key">
            <summary>
            参数标识符 (JSON中的路径，支持多层嵌套，如: "IO.input.channel1" 或 "sensors.temperature")
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceParaInput.DataType">
            <summary>
            数据类型（1:decimal 模拟量 2:string字符串 3:datetime时间 4:json 5:enum枚举）
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceParaInput.EnumDescription">
            <summary>
            枚举说明
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceParaInput.Unit">
            <summary>
            单位
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceParaInput.DivisionFactor">
            <summary>
            倍率
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceParaInput.DecimalPlaces">
            <summary>
            小数位数
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceParaInput.CorrectionScale">
            <summary>
            校正比例（默认为1）
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceParaInput.CorrectionAmplitude">
            <summary>
            校正幅度（默认为0）
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceParaInput.AlarmUpperLimit">
            <summary>
            报警上限
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceParaInput.AlarmUpperLimitClearValue">
            <summary>
            报警上限解除值
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceParaInput.AlarmLowerLimit">
            <summary>
            报警下限
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceParaInput.AlarmLowerLimitClearValue">
            <summary>
            报警下限解除值
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceParaInput.WarningUpperLimit">
            <summary>
            预警上限
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceParaInput.WarningUpperLimitClearValue">
            <summary>
            预警上限解除值
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceParaInput.WarningLowerLimit">
            <summary>
            预警下限
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceParaInput.WarningLowerLimitClearValue">
            <summary>
            预警下限解除值
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceParaInput.IsSave">
            <summary>
            是否保存
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceParaInput.SaveAmplitude">
            <summary>
            保存幅度
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceParaInput.SaveAmplitudeType">
            <summary>
            保存幅度类型（0: 数值 1：百分比）
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceParaInput.SaveInterval">
            <summary>
            保存间隔(毫秒)
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceParaInput.MonitorStatus">
            <summary>
            监控状态（0: 不启用 1：启用）
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceParaInput.Description">
            <summary>
            参数描述
            </summary>
        </member>
        <member name="T:Admin.Application.DeviceServices.Dto.DeviceParaOutput">
            <summary>
            设备参数输出DTO
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceParaOutput.ParameterId">
            <summary>
            参数ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceParaOutput.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceParaOutput.InstructionId">
            <summary>
            指令ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceParaOutput.Name">
            <summary>
            参数名称
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceParaOutput.Sort">
            <summary>
            参数序号
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceParaOutput.Key">
            <summary>
            参数标识符 (JSON中的路径，支持多层嵌套，如: "IO.input.channel1" 或 "sensors.temperature")
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceParaOutput.DataType">
            <summary>
            数据类型（1:decimal 模拟量 2:string字符串 3:datetime时间 4:json 5:enum枚举）
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceParaOutput.EnumDescription">
            <summary>
            枚举说明
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceParaOutput.Unit">
            <summary>
            单位
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceParaOutput.DivisionFactor">
            <summary>
            倍率
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceParaOutput.DecimalPlaces">
            <summary>
            小数位数
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceParaOutput.CorrectionScale">
            <summary>
            校正比例
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceParaOutput.CorrectionAmplitude">
            <summary>
            校正幅度
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceParaOutput.AlarmUpperLimit">
            <summary>
            报警上限
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceParaOutput.AlarmUpperLimitClearValue">
            <summary>
            报警上限解除值
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceParaOutput.AlarmLowerLimit">
            <summary>
            报警下限
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceParaOutput.AlarmLowerLimitClearValue">
            <summary>
            报警下限解除值
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceParaOutput.WarningUpperLimit">
            <summary>
            预警上限
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceParaOutput.WarningUpperLimitClearValue">
            <summary>
            预警上限解除值
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceParaOutput.WarningLowerLimit">
            <summary>
            预警下限
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceParaOutput.WarningLowerLimitClearValue">
            <summary>
            预警下限解除值
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceParaOutput.IsSave">
            <summary>
            是否保存
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceParaOutput.SaveAmplitude">
            <summary>
            保存幅度
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceParaOutput.SaveAmplitudeType">
            <summary>
            保存幅度类型（0: 数值 1：百分比）
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceParaOutput.SaveInterval">
            <summary>
            保存间隔(毫秒)
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceParaOutput.MonitorStatus">
            <summary>
            监控状态（0: 不启用 1：启用）
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceParaOutput.Description">
            <summary>
            参数描述
            </summary>
        </member>
        <member name="T:Admin.Application.DeviceServices.Dto.DeviceParaSimpleOutput">
            <summary>
            设备参数简单输出DTO（用于下拉选择等场景）
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceParaSimpleOutput.ParameterId">
            <summary>
            参数ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceParaSimpleOutput.Name">
            <summary>
            参数名称
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceParaSimpleOutput.Key">
            <summary>
            参数标识符
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceParaSimpleOutput.DataType">
            <summary>
            数据类型（1:decimal 模拟量 2:string字符串 3:datetime时间 4:json 5:enum枚举）
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceParaSimpleOutput.Unit">
            <summary>
            单位
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceParaSimpleOutput.MonitorStatus">
            <summary>
            监控状态（0: 不启用 1：启用）
            </summary>
        </member>
        <member name="T:Admin.Application.DeviceServices.Dto.BatchDeleteDeviceParaInput">
            <summary>
            批量删除设备参数输入DTO
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.BatchDeleteDeviceParaInput.ParameterIds">
            <summary>
            参数ID列表
            </summary>
        </member>
        <member name="T:Admin.Application.DeviceServices.Dto.SetDeviceParaMonitorStatusInput">
            <summary>
            设置设备参数监控状态输入DTO
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.SetDeviceParaMonitorStatusInput.ParameterIds">
            <summary>
            参数ID列表
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.SetDeviceParaMonitorStatusInput.MonitorStatus">
            <summary>
            监控状态（0: 不启用 1：启用）
            </summary>
        </member>
        <member name="T:Admin.Application.DeviceServices.Dto.DeviceTopicConfigQueryInput">
            <summary>
            设备主题配置查询输入DTO
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceTopicConfigQueryInput.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceTopicConfigQueryInput.TemplateName">
            <summary>
            模板名称
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceTopicConfigQueryInput.Topic">
            <summary>
            主题
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceTopicConfigQueryInput.AccessType">
            <summary>
            访问类型(0=All, 1=Publish, 2=Subscribe)
            </summary>
        </member>
        <member name="T:Admin.Application.DeviceServices.Dto.AddDeviceTopicConfigInput">
            <summary>
            添加设备主题配置输入DTO
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceTopicConfigInput.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceTopicConfigInput.TemplateName">
            <summary>
            模板名称
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceTopicConfigInput.Topic">
            <summary>
            主题
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceTopicConfigInput.AccessType">
            <summary>
            访问类型(0=All, 1=Publish, 2=Subscribe)
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceTopicConfigInput.DataFormat">
            <summary>
            Payload编码 (1:JSON 2:HEX)
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceTopicConfigInput.Priority">
            <summary>
            优先级
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.AddDeviceTopicConfigInput.Description">
            <summary>
            模板描述
            </summary>
        </member>
        <member name="T:Admin.Application.DeviceServices.Dto.UpdateDeviceTopicConfigInput">
            <summary>
            更新设备主题配置输入DTO
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceTopicConfigInput.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceTopicConfigInput.TemplateName">
            <summary>
            模板名称
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceTopicConfigInput.Topic">
            <summary>
            主题
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceTopicConfigInput.AccessType">
            <summary>
            访问类型(0=All, 1=Publish, 2=Subscribe)
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceTopicConfigInput.DataFormat">
            <summary>
            Payload编码 (1:JSON 2:HEX)
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceTopicConfigInput.Priority">
            <summary>
            优先级
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceTopicConfigInput.Description">
            <summary>
            模板描述
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdateDeviceTopicConfigInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Admin.Application.DeviceServices.Dto.DeviceTopicConfigOutput">
            <summary>
            设备主题配置输出DTO
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceTopicConfigOutput.Id">
            <summary>
            配置ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceTopicConfigOutput.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceTopicConfigOutput.TemplateName">
            <summary>
            模板名称
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceTopicConfigOutput.Topic">
            <summary>
            主题
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceTopicConfigOutput.AccessType">
            <summary>
            访问类型(0=All, 1=Publish, 2=Subscribe)
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceTopicConfigOutput.AccessTypeDesc">
            <summary>
            访问类型描述
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceTopicConfigOutput.DataFormat">
            <summary>
            Payload编码 (1:JSON 2:HEX)
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceTopicConfigOutput.DataFormatDesc">
            <summary>
            数据格式描述
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceTopicConfigOutput.Priority">
            <summary>
            优先级
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceTopicConfigOutput.Description">
            <summary>
            模板描述
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceTopicConfigOutput.CreateBy">
            <summary>
            创建人
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceTopicConfigOutput.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceTopicConfigOutput.UpdateBy">
            <summary>
            修改人
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceTopicConfigOutput.UpdateTime">
            <summary>
            修改时间
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceTopicConfigOutput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Admin.Application.DeviceServices.Dto.DeviceTopicConfigSimpleOutput">
            <summary>
            设备主题配置简单输出DTO (用于下拉选择等场景)
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceTopicConfigSimpleOutput.Id">
            <summary>
            配置ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceTopicConfigSimpleOutput.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceTopicConfigSimpleOutput.TemplateName">
            <summary>
            模板名称
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceTopicConfigSimpleOutput.Topic">
            <summary>
            主题
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceTopicConfigSimpleOutput.AccessTypeDesc">
            <summary>
            访问类型描述
            </summary>
        </member>
        <member name="T:Admin.Application.DeviceServices.Dto.CopyDeviceTopicConfigInput">
            <summary>
            复制设备主题配置输入DTO
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.CopyDeviceTopicConfigInput.SourceDeviceId">
            <summary>
            源设备ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.CopyDeviceTopicConfigInput.TargetDeviceId">
            <summary>
            目标设备ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.CopyDeviceTopicConfigInput.Overwrite">
            <summary>
            是否覆盖已存在的配置
            </summary>
        </member>
        <member name="T:Admin.Application.DeviceServices.Dto.DeviceTopicConfigStatistics">
            <summary>
            设备主题配置统计信息
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceTopicConfigStatistics.TotalCount">
            <summary>
            总配置数量
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceTopicConfigStatistics.PublishCount">
            <summary>
            发布类型配置数量
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceTopicConfigStatistics.SubscribeCount">
            <summary>
            订阅类型配置数量
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceTopicConfigStatistics.AllAccessCount">
            <summary>
            全部访问类型配置数量
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceTopicConfigStatistics.JsonFormatCount">
            <summary>
            JSON格式配置数量
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.DeviceTopicConfigStatistics.HexFormatCount">
            <summary>
            HEX格式配置数量
            </summary>
        </member>
        <member name="T:Admin.Application.DeviceServices.Dto.UpdatePriorityInput">
            <summary>
            更新优先级输入DTO
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdatePriorityInput.Id">
            <summary>
            配置ID
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.UpdatePriorityInput.Priority">
            <summary>
            优先级
            </summary>
        </member>
        <member name="T:Admin.Application.DeviceServices.Dto.BatchUpdatePriorityInput">
            <summary>
            批量更新优先级输入DTO
            </summary>
        </member>
        <member name="P:Admin.Application.DeviceServices.Dto.BatchUpdatePriorityInput.Updates">
            <summary>
            优先级更新列表
            </summary>
        </member>
        <member name="T:Admin.Application.DictCategoryServices.DictCategoryService">
            <summary>
            字典分类服务
            </summary>
        </member>
        <member name="M:Admin.Application.DictCategoryServices.DictCategoryService.#ctor(SqlSugar.ISqlSugarClient,Admin.SqlSugar.Repository{Admin.SqlSugar.Entity.DictCategoryEntity})">
            <summary>
            字典分类服务
            </summary>
        </member>
        <member name="M:Admin.Application.DictCategoryServices.DictCategoryService.GetPagedListAsync(Admin.Application.DictCategoryServices.Dtos.GetPagedListInput)">
            <summary>
            分页查询
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.DictCategoryServices.DictCategoryService.GetAsync(System.Int64)">
            <summary>
            单条查询
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.DictCategoryServices.DictCategoryService.AddAsync(Admin.Application.DictCategoryServices.Dtos.AddDictCategoryInput)">
            <summary>
            添加
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.DictCategoryServices.DictCategoryService.PutAsync(System.Int64,Admin.Application.DictCategoryServices.Dtos.AddDictCategoryInput)">
            <summary>
            编辑
            </summary>
            <param name="id"></param>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.DictCategoryServices.DictCategoryService.DeleteAsync(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="T:Admin.Application.DictCategoryServices.Dtos.AddDictCategoryInput">
            <summary>
            字典分类添加
            </summary>
        </member>
        <member name="P:Admin.Application.DictCategoryServices.Dtos.AddDictCategoryInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.Application.DictCategoryServices.Dtos.AddDictCategoryInput.Name">
            <summary>
            分类名称
            </summary>
        </member>
        <member name="P:Admin.Application.DictCategoryServices.Dtos.AddDictCategoryInput.Code">
            <summary>
            分类编码
            </summary>
        </member>
        <member name="T:Admin.Application.DictCategoryServices.Dtos.DictCategoryOutput">
            <summary>
            字典分类详情
            </summary>
        </member>
        <member name="P:Admin.Application.DictCategoryServices.Dtos.DictCategoryOutput.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:Admin.Application.DictCategoryServices.Dtos.DictCategoryOutput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.Application.DictCategoryServices.Dtos.DictCategoryOutput.Name">
            <summary>
            分类名称
            </summary>
        </member>
        <member name="P:Admin.Application.DictCategoryServices.Dtos.DictCategoryOutput.Code">
            <summary>
            分类编码
            </summary>
        </member>
        <member name="T:Admin.Application.DictCategoryServices.Dtos.GetPagedListInput">
            <summary>
            字典分类查询
            </summary>
        </member>
        <member name="P:Admin.Application.DictCategoryServices.Dtos.GetPagedListInput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="T:Admin.Application.DictDataServices.DictDataService">
            <summary>
            字典数据服务
            </summary>
        </member>
        <member name="M:Admin.Application.DictDataServices.DictDataService.#ctor(SqlSugar.ISqlSugarClient,Admin.SqlSugar.Repository{Admin.SqlSugar.Entity.DictDataEntity})">
            <summary>
            字典数据服务
            </summary>
        </member>
        <member name="M:Admin.Application.DictDataServices.DictDataService.GetPagedListAsync(Admin.Application.DictDataServices.Dtos.GetPagedListInput)">
            <summary>
            分页查询
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.DictDataServices.DictDataService.GetListAsync(System.String)">
            <summary>
            查询分类下的所有数据
            </summary>
            <param name="categoryCode"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.DictDataServices.DictDataService.GetAsync(System.Int64)">
            <summary>
            单条查询
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.DictDataServices.DictDataService.AddAsync(Admin.Application.DictDataServices.Dtos.AddDictDataInput)">
            <summary>
            添加
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.DictDataServices.DictDataService.PutAsync(System.Int64,Admin.Application.DictDataServices.Dtos.AddDictDataInput)">
            <summary>
            编辑
            </summary>
            <param name="id"></param>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.DictDataServices.DictDataService.DeleteAsync(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="T:Admin.Application.DictDataServices.Dtos.AddDictDataInput">
            <summary>
            字典数据添加
            </summary>
        </member>
        <member name="P:Admin.Application.DictDataServices.Dtos.AddDictDataInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.Application.DictDataServices.Dtos.AddDictDataInput.CategoryId">
            <summary>
            字典分类ID
            </summary>
        </member>
        <member name="P:Admin.Application.DictDataServices.Dtos.AddDictDataInput.Name">
            <summary>
            字典名称
            </summary>
        </member>
        <member name="P:Admin.Application.DictDataServices.Dtos.AddDictDataInput.Sort">
            <summary>
            排序
            </summary>
        </member>
        <member name="T:Admin.Application.DictDataServices.Dtos.DictDataOutput">
            <summary>
            字典数据详情
            </summary>
        </member>
        <member name="P:Admin.Application.DictDataServices.Dtos.DictDataOutput.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:Admin.Application.DictDataServices.Dtos.DictDataOutput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.Application.DictDataServices.Dtos.DictDataOutput.CategoryId">
            <summary>
            字典分类ID
            </summary>
        </member>
        <member name="P:Admin.Application.DictDataServices.Dtos.DictDataOutput.Name">
            <summary>
            字典名称
            </summary>
        </member>
        <member name="P:Admin.Application.DictDataServices.Dtos.DictDataOutput.Sort">
            <summary>
            排序
            </summary>
        </member>
        <member name="T:Admin.Application.DictDataServices.Dtos.GetPagedListInput">
            <summary>
            字典数据查询
            </summary>
        </member>
        <member name="P:Admin.Application.DictDataServices.Dtos.GetPagedListInput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:Admin.Application.DictDataServices.Dtos.GetPagedListInput.CategoryId">
            <summary>
            字典分类Id
            </summary>
        </member>
        <member name="T:Admin.Application.FunctionServices.Dtos.AddFunctionInput">
            <summary>
            组织机构添加
            </summary>
        </member>
        <member name="P:Admin.Application.FunctionServices.Dtos.AddFunctionInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.Application.FunctionServices.Dtos.AddFunctionInput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:Admin.Application.FunctionServices.Dtos.AddFunctionInput.Code">
            <summary>
            编码
            </summary>
        </member>
        <member name="P:Admin.Application.FunctionServices.Dtos.AddFunctionInput.ParentId">
            <summary>
            父级Id
            </summary>
        </member>
        <member name="P:Admin.Application.FunctionServices.Dtos.AssignInterfaceInput.FunctionId">
            <summary>
            功能Id
            </summary>
        </member>
        <member name="P:Admin.Application.FunctionServices.Dtos.AssignInterfaceInput.InterfaceId">
            <summary>
            接口Id
            </summary>
        </member>
        <member name="P:Admin.Application.FunctionServices.Dtos.BindedInterfaceOutput.Id">
            <summary>
            Id
            </summary>
        </member>
        <member name="P:Admin.Application.FunctionServices.Dtos.BindedInterfaceOutput.Name">
            <summary>
            接口名称
            </summary>
        </member>
        <member name="P:Admin.Application.FunctionServices.Dtos.BindedInterfaceOutput.Path">
            <summary>
            接口地址
            </summary>
        </member>
        <member name="P:Admin.Application.FunctionServices.Dtos.BindedInterfaceOutput.InterfaceId">
            <summary>
            接口Id 
            </summary>
        </member>
        <member name="T:Admin.Application.FunctionServices.Dtos.FunctionOutput">
            <summary>
            组织机构详情
            </summary>
        </member>
        <member name="P:Admin.Application.FunctionServices.Dtos.FunctionOutput.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:Admin.Application.FunctionServices.Dtos.FunctionOutput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.Application.FunctionServices.Dtos.FunctionOutput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:Admin.Application.FunctionServices.Dtos.FunctionOutput.Code">
            <summary>
            编码
            </summary>
        </member>
        <member name="P:Admin.Application.FunctionServices.Dtos.FunctionOutput.ParentId">
            <summary>
            父级Id
            </summary>
        </member>
        <member name="P:Admin.Application.FunctionServices.Dtos.FunctionOutput.Children">
            <summary>
            子集
            </summary>
        </member>
        <member name="T:Admin.Application.FunctionServices.Dtos.GetPagedListInput">
            <summary>
            组织机构查询
            </summary>
        </member>
        <member name="P:Admin.Application.FunctionServices.Dtos.GetPagedListInput.Name">
            <summary>
            功能名称
            </summary>
        </member>
        <member name="T:Admin.Application.FunctionServices.FunctionService">
            <summary>
            功能服务
            </summary>
        </member>
        <member name="M:Admin.Application.FunctionServices.FunctionService.#ctor(SqlSugar.ISqlSugarClient,Admin.SqlSugar.Repository{Admin.SqlSugar.Entity.FunctionEntity},Admin.Core.Cache.IAdminCache)">
            <summary>
            功能服务
            </summary>
        </member>
        <member name="M:Admin.Application.FunctionServices.FunctionService.GetPagedListAsync(Admin.Application.FunctionServices.Dtos.GetPagedListInput)">
            <summary>
            分页查询
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.FunctionServices.FunctionService.GetAsync(System.Int64)">
            <summary>
            单条查询
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.FunctionServices.FunctionService.AddAsync(Admin.Application.FunctionServices.Dtos.AddFunctionInput)">
            <summary>
            添加
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.FunctionServices.FunctionService.PutAsync(System.Int64,Admin.Application.FunctionServices.Dtos.AddFunctionInput)">
            <summary>
            编辑
            </summary>
            <param name="id"></param>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.FunctionServices.FunctionService.DeleteAsync(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.FunctionServices.FunctionService.GetTreeAsync">
            <summary>
            功能树查询
            </summary>
        </member>
        <member name="M:Admin.Application.FunctionServices.FunctionService.GetInterfacesAsync(System.Int64)">
            <summary>
            获取功能拥有的接口
            </summary>
            <param name="functionId"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.FunctionServices.FunctionService.AssignInterfaceAsync(Admin.Application.FunctionServices.Dtos.AssignInterfaceInput)">
            <summary>
            给功能分配接口
            </summary>
            <param name="input"></param>
        </member>
        <member name="M:Admin.Application.FunctionServices.FunctionService.DeleteFunctionInterfaceAsync(System.Int64)">
            <summary>
            移除功能的接口
            </summary>
            <param name="id"></param>
        </member>
        <member name="P:Admin.Application.InterfaceServices.Dtos.GetPagedListInput.Path">
            <summary>
            地址
            </summary>
        </member>
        <member name="T:Admin.Application.InterfaceServices.Dtos.InterfaceGroupOutput">
            <summary>
            接口组
            </summary>
        </member>
        <member name="P:Admin.Application.InterfaceServices.Dtos.InterfaceGroupOutput.Id">
            <summary>
            Id
            </summary>
        </member>
        <member name="P:Admin.Application.InterfaceServices.Dtos.InterfaceGroupOutput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:Admin.Application.InterfaceServices.Dtos.InterfaceGroupOutput.Code">
            <summary>
            编码
            </summary>
        </member>
        <member name="P:Admin.Application.InterfaceServices.Dtos.InterfaceGroupOutput.Interfaces">
            <summary>
            接口详情
            </summary>
        </member>
        <member name="T:Admin.Application.InterfaceServices.Dtos.InterfaceOutput">
            <summary>
            接口详情
            </summary>
        </member>
        <member name="P:Admin.Application.InterfaceServices.Dtos.InterfaceOutput.Id">
            <summary>
            id
            </summary>
        </member>
        <member name="P:Admin.Application.InterfaceServices.Dtos.InterfaceOutput.Name">
            <summary>
            接口名称
            </summary>
        </member>
        <member name="P:Admin.Application.InterfaceServices.Dtos.InterfaceOutput.Path">
            <summary>
            接口地址
            </summary>
        </member>
        <member name="P:Admin.Application.InterfaceServices.Dtos.InterfaceOutput.RequestMethod">
            <summary>
            请求方法
            </summary>
        </member>
        <member name="P:Admin.Application.InterfaceServices.Dtos.InterfaceOutput.GroupId">
            <summary>
            分组Id
            </summary>
        </member>
        <member name="T:Admin.Application.InterfaceServices.InterfaceService">
            <summary>
            接口服务
            </summary>
        </member>
        <member name="M:Admin.Application.InterfaceServices.InterfaceService.#ctor(SqlSugar.ISqlSugarClient,Microsoft.AspNetCore.Mvc.ApiExplorer.IApiDescriptionGroupCollectionProvider)">
            <summary>
            接口服务
            </summary>
        </member>
        <member name="F:Admin.Application.InterfaceServices.InterfaceService._db">
            <summary>
            db
            </summary>
        </member>
        <member name="F:Admin.Application.InterfaceServices.InterfaceService._apiDescriptionGroupCollectionProvider">
            <summary>
            apiDescriptionGroupCollectionProvider
            </summary>
        </member>
        <member name="M:Admin.Application.InterfaceServices.InterfaceService.GetPagedListAsync(Admin.Application.InterfaceServices.Dtos.GetPagedListInput)">
            <summary>
            分页查询
            </summary>
        </member>
        <member name="M:Admin.Application.InterfaceServices.InterfaceService.AsyncApi">
            <summary>
            同步接口
            </summary>
            <returns></returns>
        </member>
        <member name="T:Admin.Application.ModbusServices.ModbusInstructionManagementService">
            <summary>
            Modbus指令管理服务
            提供Modbus指令调度的管理和监控功能
            </summary>
        </member>
        <member name="M:Admin.Application.ModbusServices.ModbusInstructionManagementService.#ctor(SqlSugar.ISqlSugarClient,Microsoft.Extensions.Logging.ILogger{Admin.Application.ModbusServices.ModbusInstructionManagementService})">
            <summary>
            Modbus指令管理服务
            提供Modbus指令调度的管理和监控功能
            </summary>
        </member>
        <member name="M:Admin.Application.ModbusServices.ModbusInstructionManagementService.GetModbusDevicesAsync">
            <summary>
            获取Modbus设备列表
            </summary>
            <returns>Modbus设备列表</returns>
        </member>
        <member name="M:Admin.Application.ModbusServices.ModbusInstructionManagementService.GetDeviceInstructionsAsync(System.Int64)">
            <summary>
            获取设备的指令列表
            </summary>
            <param name="deviceId">设备ID</param>
            <returns>指令列表</returns>
        </member>
        <member name="M:Admin.Application.ModbusServices.ModbusInstructionManagementService.UpdateInstructionAsync(Admin.Application.ModbusServices.UpdateModbusInstructionInput)">
            <summary>
            更新指令参数
            </summary>
            <param name="input">更新参数</param>
            <returns>更新结果</returns>
        </member>
        <member name="M:Admin.Application.ModbusServices.ModbusInstructionManagementService.SetInstructionEnabledAsync(System.Int64,System.Boolean)">
            <summary>
            启用/禁用指令
            </summary>
            <param name="instructionId">指令ID</param>
            <param name="enabled">是否启用</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:Admin.Application.ModbusServices.ModbusInstructionManagementService.SetDeviceInstructionsEnabledAsync(System.Int64,System.Boolean)">
            <summary>
            批量启用/禁用设备的所有指令
            </summary>
            <param name="deviceId">设备ID</param>
            <param name="enabled">是否启用</param>
            <returns>操作结果</returns>
        </member>
        <member name="T:Admin.Application.ModbusServices.ModbusDeviceOutput">
            <summary>
            Modbus设备输出DTO
            </summary>
        </member>
        <member name="T:Admin.Application.ModbusServices.ModbusInstructionOutput">
            <summary>
            Modbus指令输出DTO
            </summary>
        </member>
        <member name="T:Admin.Application.ModbusServices.UpdateModbusInstructionInput">
            <summary>
            更新Modbus指令输入DTO
            </summary>
        </member>
        <member name="T:Admin.Application.ModbusServices.ModbusTestService">
            <summary>
            Modbus测试服务
            提供Modbus指令测试和调试功能
            </summary>
        </member>
        <member name="M:Admin.Application.ModbusServices.ModbusTestService.#ctor(SqlSugar.ISqlSugarClient,Admin.Communication.Mqtt.Services.MqttBrokerService,Microsoft.Extensions.Logging.ILogger{Admin.Application.ModbusServices.ModbusTestService})">
            <summary>
            Modbus测试服务
            提供Modbus指令测试和调试功能
            </summary>
        </member>
        <member name="M:Admin.Application.ModbusServices.ModbusTestService.SendTestCommandAsync(Admin.Application.ModbusServices.ModbusTestInput)">
            <summary>
            手动发送Modbus指令
            </summary>
            <param name="input">测试输入</param>
            <returns>发送结果</returns>
        </member>
        <member name="M:Admin.Application.ModbusServices.ModbusTestService.SimulateDeviceResponseAsync(Admin.Application.ModbusServices.ModbusResponseInput)">
            <summary>
            模拟设备响应
            </summary>
            <param name="input">响应输入</param>
            <returns>模拟结果</returns>
        </member>
        <member name="M:Admin.Application.ModbusServices.ModbusTestService.GetDeviceInstructionsForTestAsync(System.Int64)">
            <summary>
            获取设备指令列表（用于测试）
            </summary>
            <param name="deviceId">设备ID</param>
            <returns>指令列表</returns>
        </member>
        <member name="M:Admin.Application.ModbusServices.ModbusTestService.GenerateModbusCommand(Admin.Application.ModbusServices.ModbusCommandInput)">
            <summary>
            生成标准Modbus RTU指令
            </summary>
            <param name="input">指令参数</param>
            <returns>生成的指令</returns>
        </member>
        <member name="M:Admin.Application.ModbusServices.ModbusTestService.CalculateCRC16(System.Byte[])">
            <summary>
            简化的CRC16计算
            </summary>
        </member>
        <member name="T:Admin.Application.ModbusServices.ModbusTestInput">
            <summary>
            Modbus测试输入
            </summary>
        </member>
        <member name="T:Admin.Application.ModbusServices.ModbusResponseInput">
            <summary>
            Modbus响应输入
            </summary>
        </member>
        <member name="T:Admin.Application.ModbusServices.ModbusTestResult">
            <summary>
            Modbus测试结果
            </summary>
        </member>
        <member name="T:Admin.Application.ModbusServices.DeviceInstructionTestOutput">
            <summary>
            设备指令测试输出
            </summary>
        </member>
        <member name="T:Admin.Application.ModbusServices.ModbusCommandInput">
            <summary>
            Modbus指令输入
            </summary>
        </member>
        <member name="T:Admin.Application.ModbusServices.ModbusCommandResult">
            <summary>
            Modbus指令生成结果
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.GetAclRulesRequest">
            <summary>
            获取ACL规则请求DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetAclRulesRequest.RuleName">
            <summary>
            规则名称过滤
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetAclRulesRequest.AccessType">
            <summary>
            访问类型过滤
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetAclRulesRequest.Username">
            <summary>
            用户名过滤
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetAclRulesRequest.ClientId">
            <summary>
            客户端ID过滤
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetAclRulesRequest.Topic">
            <summary>
            主题过滤
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetAclRulesRequest.Permission">
            <summary>
            权限过滤 (allow/deny)
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetAclRulesRequest.IsActive">
            <summary>
            是否激活过滤
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetAclRulesRequest.Tags">
            <summary>
            标签过滤
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.AddAclRuleRequest">
            <summary>
            添加ACL规则请求DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AddAclRuleRequest.RuleName">
            <summary>
            规则名称
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AddAclRuleRequest.Priority">
            <summary>
            优先级 (数值越大优先级越高)
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AddAclRuleRequest.AccessType">
            <summary>
            访问类型
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AddAclRuleRequest.Allow">
            <summary>
            权限 (true=允许, false=拒绝)
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AddAclRuleRequest.Username">
            <summary>
            用户名 (为空表示适用于所有用户)
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AddAclRuleRequest.ClientId">
            <summary>
            客户端ID (为空表示适用于所有客户端)
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AddAclRuleRequest.IpAddress">
            <summary>
            IP地址 (为空表示适用于所有IP)
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AddAclRuleRequest.Topic">
            <summary>
            主题 (支持通配符)
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AddAclRuleRequest.Description">
            <summary>
            规则描述
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AddAclRuleRequest.IsActive">
            <summary>
            是否激活
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AddAclRuleRequest.EffectiveStartTime">
            <summary>
            生效开始时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AddAclRuleRequest.EffectiveEndTime">
            <summary>
            生效结束时间
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.UpdateAclRuleRequest">
            <summary>
            更新ACL规则请求DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.UpdateAclRuleRequest.RuleName">
            <summary>
            规则名称
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.UpdateAclRuleRequest.Priority">
            <summary>
            优先级 (数值越大优先级越高)
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.UpdateAclRuleRequest.AccessType">
            <summary>
            访问类型
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.UpdateAclRuleRequest.Allow">
            <summary>
            权限 (true=允许, false=拒绝)
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.UpdateAclRuleRequest.Username">
            <summary>
            用户名 (为空表示适用于所有用户)
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.UpdateAclRuleRequest.ClientId">
            <summary>
            客户端ID (为空表示适用于所有客户端)
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.UpdateAclRuleRequest.IpAddress">
            <summary>
            IP地址 (为空表示适用于所有IP)
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.UpdateAclRuleRequest.Topic">
            <summary>
            主题 (支持通配符)
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.UpdateAclRuleRequest.Description">
            <summary>
            规则描述
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.UpdateAclRuleRequest.IsActive">
            <summary>
            是否激活
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.UpdateAclRuleRequest.EffectiveStartTime">
            <summary>
            生效开始时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.UpdateAclRuleRequest.EffectiveEndTime">
            <summary>
            生效结束时间
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.TestPermissionRequest">
            <summary>
            测试权限请求DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.TestPermissionRequest.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.TestPermissionRequest.Username">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.TestPermissionRequest.Topic">
            <summary>
            主题
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.TestPermissionRequest.AccessType">
            <summary>
            访问类型
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.AclRuleDto">
            <summary>
            ACL规则DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclRuleDto.Id">
            <summary>
            规则ID
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclRuleDto.RuleName">
            <summary>
            规则名称
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclRuleDto.Priority">
            <summary>
            优先级
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclRuleDto.AccessType">
            <summary>
            访问类型
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclRuleDto.Permission">
            <summary>
            权限
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclRuleDto.Allow">
            <summary>
            是否允许
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclRuleDto.Username">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclRuleDto.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclRuleDto.IpAddress">
            <summary>
            IP地址
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclRuleDto.Topic">
            <summary>
            主题
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclRuleDto.Description">
            <summary>
            规则描述
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclRuleDto.IsActive">
            <summary>
            是否激活
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclRuleDto.EffectiveStartTime">
            <summary>
            生效开始时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclRuleDto.EffectiveEndTime">
            <summary>
            生效结束时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclRuleDto.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclRuleDto.UpdateTime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclRuleDto.CreateBy">
            <summary>
            创建者
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclRuleDto.UpdateBy">
            <summary>
            更新者
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclRuleDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.PermissionTestResultDto">
            <summary>
            权限测试结果DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PermissionTestResultDto.IsAuthorized">
            <summary>
            是否被授权
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PermissionTestResultDto.MatchedRule">
            <summary>
            匹配的规则
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PermissionTestResultDto.TestTime">
            <summary>
            测试时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PermissionTestResultDto.FailureReason">
            <summary>
            失败原因
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PermissionTestResultDto.AllMatchedRules">
            <summary>
            所有匹配的规则
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.MatchedRuleDto">
            <summary>
            匹配的规则DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MatchedRuleDto.Id">
            <summary>
            规则ID
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MatchedRuleDto.Priority">
            <summary>
            优先级
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MatchedRuleDto.Permission">
            <summary>
            权限
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MatchedRuleDto.MatchedPattern">
            <summary>
            匹配的模式
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MatchedRuleDto.MatchType">
            <summary>
            匹配类型
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.GetAclRulesResponse">
            <summary>
            ACL规则列表响应DTO
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.AclManagementResult">
            <summary>
            ACL管理结果基类
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclManagementResult.IsSuccess">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclManagementResult.ErrorMessage">
            <summary>
            错误消息
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclManagementResult.Data">
            <summary>
            数据
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.AclRuleResult">
            <summary>
            ACL规则操作结果
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclRuleResult.IsSuccess">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclRuleResult.ErrorMessage">
            <summary>
            错误消息
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclRuleResult.Data">
            <summary>
            规则数据
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.PermissionTestResult">
            <summary>
            权限测试结果
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PermissionTestResult.IsSuccess">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PermissionTestResult.ErrorMessage">
            <summary>
            错误消息
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PermissionTestResult.Data">
            <summary>
            测试结果数据
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.StartBrokerInput">
            <summary>
            启动代理服务输入
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.StartBrokerInput.Port">
            <summary>
            监听端口，默认为1883
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.StartBrokerInput.Configuration">
            <summary>
            启动时的配置更新
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.RestartBrokerInput">
            <summary>
            重启代理服务输入
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.RestartBrokerInput.GracefulShutdown">
            <summary>
            是否优雅停机，默认为true
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.RestartBrokerInput.ShutdownTimeout">
            <summary>
            停机超时时间(秒)，默认为30秒
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.BrokerStartConfigurationInput">
            <summary>
            代理启动配置输入
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerStartConfigurationInput.MaxConnections">
            <summary>
            最大连接数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerStartConfigurationInput.MaxConnectionsPerIp">
            <summary>
            单个IP最大连接数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerStartConfigurationInput.ConnectionTimeout">
            <summary>
            连接超时时间(秒)
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerStartConfigurationInput.EnableTls">
            <summary>
            是否启用TLS
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerStartConfigurationInput.AllowAnonymousAccess">
            <summary>
            是否允许匿名访问
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.BrokerServiceOutput">
            <summary>
            代理服务操作输出
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerServiceOutput.Message">
            <summary>
            消息
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerServiceOutput.Port">
            <summary>
            端口
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerServiceOutput.IsRunning">
            <summary>
            是否运行中
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerServiceOutput.StartTime">
            <summary>
            启动时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerServiceOutput.StopTime">
            <summary>
            停止时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerServiceOutput.RestartTime">
            <summary>
            重启时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerServiceOutput.PreviousUptime">
            <summary>
            之前运行时长
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerServiceOutput.Timestamp">
            <summary>
            时间戳
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.BrokerStatusOutput">
            <summary>
            代理状态输出
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerStatusOutput.IsRunning">
            <summary>
            是否运行中
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerStatusOutput.Port">
            <summary>
            端口
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerStatusOutput.StartTime">
            <summary>
            启动时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerStatusOutput.Uptime">
            <summary>
            运行时长
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerStatusOutput.Version">
            <summary>
            版本
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerStatusOutput.ConnectedClientCount">
            <summary>
            已连接客户端数量
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerStatusOutput.Configuration">
            <summary>
            配置信息
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.BrokerStatusConfigurationOutput">
            <summary>
            代理状态配置输出
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerStatusConfigurationOutput.MaxConnections">
            <summary>
            最大连接数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerStatusConfigurationOutput.MaxConnectionsPerIp">
            <summary>
            单个IP最大连接数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerStatusConfigurationOutput.ConnectionTimeout">
            <summary>
            连接超时时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerStatusConfigurationOutput.EnableRetainedMessages">
            <summary>
            是否启用保留消息
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerStatusConfigurationOutput.EnableStatistics">
            <summary>
            是否启用统计
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerStatusConfigurationOutput.AllowAnonymousAccess">
            <summary>
            是否允许匿名访问
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerStatusConfigurationOutput.UseTls">
            <summary>
            是否使用TLS
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerStatusConfigurationOutput.MaxMessageSize">
            <summary>
            最大消息大小
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerStatusConfigurationOutput.MaxTopicLength">
            <summary>
            最大主题长度
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.BrokerConfigurationOutput">
            <summary>
            代理配置输出
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerConfigurationOutput.Broker">
            <summary>
            代理配置
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerConfigurationOutput.Authentication">
            <summary>
            认证配置
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerConfigurationOutput.Acl">
            <summary>
            ACL配置
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.BrokerConfigurationSectionOutput">
            <summary>
            代理配置节输出
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerConfigurationSectionOutput.Port">
            <summary>
            端口
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerConfigurationSectionOutput.MaxConnections">
            <summary>
            最大连接数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerConfigurationSectionOutput.MaxConnectionsPerIp">
            <summary>
            单个IP最大连接数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerConfigurationSectionOutput.ConnectionTimeout">
            <summary>
            连接超时时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerConfigurationSectionOutput.UseTls">
            <summary>
            是否使用TLS
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerConfigurationSectionOutput.CertificatePath">
            <summary>
            证书路径
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerConfigurationSectionOutput.AllowAnonymousAccess">
            <summary>
            是否允许匿名访问
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerConfigurationSectionOutput.EnableRetainedMessages">
            <summary>
            是否启用保留消息
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerConfigurationSectionOutput.EnableStatistics">
            <summary>
            是否启用统计
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerConfigurationSectionOutput.MaxTopicLength">
            <summary>
            最大主题长度
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerConfigurationSectionOutput.MaxMessageSize">
            <summary>
            最大消息大小
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerConfigurationSectionOutput.BindAddress">
            <summary>
            绑定地址
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerConfigurationSectionOutput.EnableWebSockets">
            <summary>
            是否启用WebSocket
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerConfigurationSectionOutput.WebSocketPort">
            <summary>
            WebSocket端口
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerConfigurationSectionOutput.WebSocketPath">
            <summary>
            WebSocket路径
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.AuthenticationConfigurationSectionOutput">
            <summary>
            认证配置节输出
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AuthenticationConfigurationSectionOutput.DefaultUsername">
            <summary>
            默认用户名
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AuthenticationConfigurationSectionOutput.DefaultPassword">
            <summary>
            默认密码（已屏蔽）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AuthenticationConfigurationSectionOutput.RequireAuthentication">
            <summary>
            是否需要认证
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AuthenticationConfigurationSectionOutput.UserCount">
            <summary>
            用户数量
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.AclConfigurationSectionOutput">
            <summary>
            ACL配置节输出
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclConfigurationSectionOutput.EnableAcl">
            <summary>
            是否启用ACL
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclConfigurationSectionOutput.RuleCount">
            <summary>
            规则数量
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.UpdateConfigurationRequest">
            <summary>
            更新配置请求
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.UpdateConfigurationRequest.Broker">
            <summary>
            代理配置更新
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.UpdateConfigurationRequest.Authentication">
            <summary>
            认证配置更新
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.UpdateConfigurationRequest.Acl">
            <summary>
            ACL配置更新
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.BrokerConfigurationUpdateDto">
            <summary>
            代理配置更新DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerConfigurationUpdateDto.Port">
            <summary>
            监听端口
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerConfigurationUpdateDto.MaxConnections">
            <summary>
            最大连接数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerConfigurationUpdateDto.MaxConnectionsPerIp">
            <summary>
            单个IP最大连接数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerConfigurationUpdateDto.ConnectionTimeout">
            <summary>
            连接超时时间(秒)
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerConfigurationUpdateDto.EnableTls">
            <summary>
            是否启用TLS
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerConfigurationUpdateDto.CertificatePath">
            <summary>
            证书路径
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerConfigurationUpdateDto.AllowAnonymousAccess">
            <summary>
            是否允许匿名访问
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerConfigurationUpdateDto.EnableRetainedMessages">
            <summary>
            是否启用保留消息
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerConfigurationUpdateDto.EnableStatistics">
            <summary>
            是否启用统计
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerConfigurationUpdateDto.MaxTopicLength">
            <summary>
            最大主题长度
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerConfigurationUpdateDto.MaxMessageSize">
            <summary>
            最大消息大小
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerConfigurationUpdateDto.BindAddress">
            <summary>
            绑定地址
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.AuthenticationConfigurationUpdateDto">
            <summary>
            认证配置更新DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AuthenticationConfigurationUpdateDto.DefaultUsername">
            <summary>
            默认用户名
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AuthenticationConfigurationUpdateDto.DefaultPassword">
            <summary>
            默认密码
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AuthenticationConfigurationUpdateDto.RequireAuthentication">
            <summary>
            是否需要认证
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.AclConfigurationUpdateDto">
            <summary>
            ACL配置更新DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclConfigurationUpdateDto.EnableAcl">
            <summary>
            是否启用ACL
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclConfigurationUpdateDto.DefaultPolicy">
            <summary>
            默认策略
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.UpdateConfigurationResponse">
            <summary>
            更新配置响应
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.UpdateConfigurationResponse.UpdatedTime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.UpdateConfigurationResponse.RequiresRestart">
            <summary>
            是否需要重启
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.UpdateConfigurationResponse.ChangedSettings">
            <summary>
            已更改的设置列表
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.JsonDateTimeConverter">
            <summary>
            自定义日期时间JSON转换器
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.ConnectionInfoDto">
            <summary>
            连接信息DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ConnectionInfoDto.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ConnectionInfoDto.Username">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ConnectionInfoDto.IpAddress">
            <summary>
            IP地址和端口
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ConnectionInfoDto.ConnectedTime">
            <summary>
            连接时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ConnectionInfoDto.LastActivityTime">
            <summary>
            最后活动时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ConnectionInfoDto.KeepAlive">
            <summary>
            保持连接时间(秒)
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ConnectionInfoDto.CleanSession">
            <summary>
            是否使用清除会话标志
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ConnectionInfoDto.ProtocolVersion">
            <summary>
            协议版本
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ConnectionInfoDto.Status">
            <summary>
            连接状态
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ConnectionInfoDto.SubscribedTopics">
            <summary>
            已订阅的主题列表
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.ConnectionDetailDto">
            <summary>
            连接详细信息DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ConnectionDetailDto.ConnectionDuration">
            <summary>
            连接持续时间（格式化字符串）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ConnectionDetailDto.SubscriptionDetails">
            <summary>
            订阅详情列表
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ConnectionDetailDto.Statistics">
            <summary>
            连接统计信息
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.SubscriptionDetailDto">
            <summary>
            订阅详情DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SubscriptionDetailDto.TopicFilter">
            <summary>
            主题过滤器
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SubscriptionDetailDto.Qos">
            <summary>
            服务质量等级
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SubscriptionDetailDto.SubscribedTime">
            <summary>
            订阅时间
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.ClientConnectionStatisticsDto">
            <summary>
            客户端连接统计信息DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ClientConnectionStatisticsDto.MessagesSent">
            <summary>
            发送的消息数量
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ClientConnectionStatisticsDto.MessagesReceived">
            <summary>
            接收的消息数量
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ClientConnectionStatisticsDto.BytesTransferred">
            <summary>
            传输的字节数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ClientConnectionStatisticsDto.LastMessageTime">
            <summary>
            最后消息时间
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.GetConnectionsRequest">
            <summary>
            连接查询请求DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetConnectionsRequest.ClientId">
            <summary>
            客户端ID过滤
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetConnectionsRequest.Username">
            <summary>
            用户名过滤
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetConnectionsRequest.IpAddress">
            <summary>
            IP地址过滤
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetConnectionsRequest.Status">
            <summary>
            连接状态过滤
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.GetConnectionsResponse">
            <summary>
            连接列表响应DTO
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.DisconnectConnectionRequest">
            <summary>
            断开连接请求DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.DisconnectConnectionRequest.Reason">
            <summary>
            断开原因
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.DisconnectConnectionRequest.SendDisconnectMessage">
            <summary>
            是否发送断开消息
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.BatchDisconnectRequest">
            <summary>
            批量断开连接请求DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BatchDisconnectRequest.ClientIds">
            <summary>
            客户端ID列表
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BatchDisconnectRequest.Reason">
            <summary>
            断开原因
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BatchDisconnectRequest.SendDisconnectMessage">
            <summary>
            是否发送断开消息
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.DisconnectResult">
            <summary>
            断开连接结果DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.DisconnectResult.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.DisconnectResult.Success">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.DisconnectResult.DisconnectTime">
            <summary>
            断开时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.DisconnectResult.Error">
            <summary>
            错误信息
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.BatchDisconnectResponse">
            <summary>
            批量断开连接响应DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BatchDisconnectResponse.TotalRequested">
            <summary>
            请求总数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BatchDisconnectResponse.SuccessfulDisconnects">
            <summary>
            成功断开的连接数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BatchDisconnectResponse.FailedDisconnects">
            <summary>
            失败的断开连接数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BatchDisconnectResponse.Results">
            <summary>
            详细结果
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.PagedRequestDto">
            <summary>
            基础分页请求DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PagedRequestDto.Page">
            <summary>
            页码（默认：1）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PagedRequestDto.PageSize">
            <summary>
            每页大小（默认：20）
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.PagedResponseDto`1">
            <summary>
            基础分页响应DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PagedResponseDto`1.Items">
            <summary>
            数据列表
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PagedResponseDto`1.Pagination">
            <summary>
            分页信息
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.PaginationDto">
            <summary>
            分页信息DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PaginationDto.Page">
            <summary>
            当前页
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PaginationDto.PageSize">
            <summary>
            每页大小
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PaginationDto.TotalCount">
            <summary>
            总记录数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PaginationDto.TotalPages">
            <summary>
            总页数
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.ConnectionManagementResult">
            <summary>
            连接管理操作结果
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ConnectionManagementResult.IsSuccess">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ConnectionManagementResult.Message">
            <summary>
            消息
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ConnectionManagementResult.Data">
            <summary>
            数据
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ConnectionManagementResult.ErrorMessage">
            <summary>
            错误信息
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ConnectionManagementResult.Timestamp">
            <summary>
            时间戳
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.CreateDeviceAclRulesInput">
            <summary>
            创建设备ACL规则请求DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.CreateDeviceAclRulesInput.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.CreateDeviceAclRulesInput.DeviceName">
            <summary>
            设备名称
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.CreateDeviceAclRulesInput.TopicPrefix">
            <summary>
            自定义主题前缀（可选，默认使用设备ID）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.CreateDeviceAclRulesInput.AllowSystemTopics">
            <summary>
            是否允许订阅系统主题
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.CreateDeviceAclRulesInput.CustomAllowedTopics">
            <summary>
            自定义允许的主题列表
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.CreateDeviceAclRulesInput.CustomDeniedTopics">
            <summary>
            自定义禁止的主题列表
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.DeviceAclRuleTemplate">
            <summary>
            设备ACL规则模板
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.DeviceAclRuleTemplate.RuleNameTemplate">
            <summary>
            规则名称模板
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.DeviceAclRuleTemplate.TopicTemplate">
            <summary>
            主题模板
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.DeviceAclRuleTemplate.AccessType">
            <summary>
            访问类型
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.DeviceAclRuleTemplate.DataFormat">
            <summary>
            Payload编码 (1:JSON 2:HEX)
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.DeviceAclRuleTemplate.Allow">
            <summary>
            是否允许
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.DeviceAclRuleTemplate.Priority">
            <summary>
            优先级
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.DeviceAclRuleTemplate.DescriptionTemplate">
            <summary>
            描述模板
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.DeviceAclRulesResult">
            <summary>
            设备ACL规则创建结果
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.DeviceAclRulesResult.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.DeviceAclRulesResult.CreatedRules">
            <summary>
            创建成功的规则列表
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.DeviceAclRulesResult.FailedRules">
            <summary>
            创建失败的规则列表
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.DeviceAclRulesResult.TotalRequested">
            <summary>
            总请求数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.DeviceAclRulesResult.SuccessCount">
            <summary>
            成功数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.DeviceAclRulesResult.FailCount">
            <summary>
            失败数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.DeviceAclRulesResult.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.CreatedAclRule">
            <summary>
            创建成功的ACL规则
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.CreatedAclRule.RuleId">
            <summary>
            规则ID
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.CreatedAclRule.RuleName">
            <summary>
            规则名称
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.CreatedAclRule.Topic">
            <summary>
            主题
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.CreatedAclRule.AccessType">
            <summary>
            访问类型
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.CreatedAclRule.Allow">
            <summary>
            是否允许
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.FailedAclRule">
            <summary>
            创建失败的ACL规则
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.FailedAclRule.RuleName">
            <summary>
            规则名称
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.FailedAclRule.Topic">
            <summary>
            主题
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.FailedAclRule.AccessType">
            <summary>
            访问类型
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.FailedAclRule.FailureReason">
            <summary>
            失败原因
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.DeleteDeviceAclRulesInput">
            <summary>
            批量删除设备ACL规则请求DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.DeleteDeviceAclRulesInput.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.DeleteDeviceAclRulesInput.DeleteAllRelated">
            <summary>
            是否删除所有相关规则（包括自定义规则）
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.QueryDeviceAclRulesInput">
            <summary>
            设备ACL规则查询请求DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.QueryDeviceAclRulesInput.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.QueryDeviceAclRulesInput.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.QueryDeviceAclRulesInput.TopicFilter">
            <summary>
            主题过滤
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.QueryDeviceAclRulesInput.AccessType">
            <summary>
            访问类型过滤
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.QueryDeviceAclRulesInput.OnlyActive">
            <summary>
            是否只查询激活的规则
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.PublishMessageRequest">
            <summary>
            发布消息请求DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PublishMessageRequest.Topic">
            <summary>
            主题
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PublishMessageRequest.Payload">
            <summary>
            消息负载
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PublishMessageRequest.Qos">
            <summary>
            服务质量等级 (0, 1, 2)
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PublishMessageRequest.Retain">
            <summary>
            是否保留消息
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PublishMessageRequest.Encoding">
            <summary>
            编码方式
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PublishMessageRequest.PublisherId">
            <summary>
            发布者标识（可选，用于审计）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PublishMessageRequest.Description">
            <summary>
            发布备注（可选）
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.BatchPublishRequest">
            <summary>
            批量发布消息请求DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BatchPublishRequest.Messages">
            <summary>
            消息列表
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BatchPublishRequest.PublisherId">
            <summary>
            发布者标识（可选，用于审计）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BatchPublishRequest.Description">
            <summary>
            批量发布备注（可选）
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.SinglePublishMessage">
            <summary>
            单条发布消息DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SinglePublishMessage.Topic">
            <summary>
            主题
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SinglePublishMessage.Payload">
            <summary>
            消息负载
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SinglePublishMessage.Qos">
            <summary>
            服务质量等级 (0, 1, 2)
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SinglePublishMessage.Retain">
            <summary>
            是否保留消息
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SinglePublishMessage.Encoding">
            <summary>
            编码方式
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.GetRetainedMessagesRequest">
            <summary>
            获取保留消息请求DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetRetainedMessagesRequest.TopicPattern">
            <summary>
            主题模式过滤
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetRetainedMessagesRequest.MinPayloadSize">
            <summary>
            最小负载大小过滤
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetRetainedMessagesRequest.MaxPayloadSize">
            <summary>
            最大负载大小过滤
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetRetainedMessagesRequest.StartTime">
            <summary>
            开始时间过滤
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetRetainedMessagesRequest.EndTime">
            <summary>
            结束时间过滤
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetRetainedMessagesRequest.SortBy">
            <summary>
            排序字段
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetRetainedMessagesRequest.SortDirection">
            <summary>
            排序方向
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.ClearRetainedMessageRequest">
            <summary>
            清除保留消息请求DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ClearRetainedMessageRequest.Reason">
            <summary>
            清除原因
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ClearRetainedMessageRequest.OperatorId">
            <summary>
            操作者
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.BatchClearRetainedMessagesRequest">
            <summary>
            批量清除保留消息请求DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BatchClearRetainedMessagesRequest.Topics">
            <summary>
            主题列表
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BatchClearRetainedMessagesRequest.Reason">
            <summary>
            清除原因
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BatchClearRetainedMessagesRequest.OperatorId">
            <summary>
            操作者
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.PublishMessageResponse">
            <summary>
            发布消息响应DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PublishMessageResponse.MessageId">
            <summary>
            消息ID
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PublishMessageResponse.Topic">
            <summary>
            主题
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PublishMessageResponse.Qos">
            <summary>
            服务质量等级
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PublishMessageResponse.Retain">
            <summary>
            是否保留消息
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PublishMessageResponse.PublishTime">
            <summary>
            发布时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PublishMessageResponse.SubscriberCount">
            <summary>
            订阅者数量
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PublishMessageResponse.PayloadSize">
            <summary>
            负载大小（字节）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PublishMessageResponse.Status">
            <summary>
            发布状态
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PublishMessageResponse.ErrorMessage">
            <summary>
            错误信息（如果发布失败）
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.BatchPublishResponse">
            <summary>
            批量发布响应DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BatchPublishResponse.TotalMessages">
            <summary>
            总消息数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BatchPublishResponse.SuccessfulPublishes">
            <summary>
            成功发布的消息数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BatchPublishResponse.FailedPublishes">
            <summary>
            失败的发布数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BatchPublishResponse.PublishTime">
            <summary>
            批量发布时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BatchPublishResponse.Results">
            <summary>
            详细结果
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BatchPublishResponse.Statistics">
            <summary>
            总体统计
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.SinglePublishResult">
            <summary>
            单条发布结果DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SinglePublishResult.Topic">
            <summary>
            主题
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SinglePublishResult.Success">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SinglePublishResult.MessageId">
            <summary>
            消息ID（成功时）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SinglePublishResult.PublishTime">
            <summary>
            发布时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SinglePublishResult.SubscriberCount">
            <summary>
            订阅者数量
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SinglePublishResult.Error">
            <summary>
            错误信息（失败时）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SinglePublishResult.PayloadSize">
            <summary>
            负载大小（字节）
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.BatchPublishStatistics">
            <summary>
            批量发布统计DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BatchPublishStatistics.TotalSubscribers">
            <summary>
            总订阅者数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BatchPublishStatistics.TotalPayloadSize">
            <summary>
            总负载大小（字节）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BatchPublishStatistics.AveragePayloadSize">
            <summary>
            平均负载大小（字节）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BatchPublishStatistics.ProcessingTimeMs">
            <summary>
            处理耗时（毫秒）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BatchPublishStatistics.MessagesPerSecond">
            <summary>
            每秒处理消息数
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.RetainedMessageDto">
            <summary>
            保留消息DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.RetainedMessageDto.Topic">
            <summary>
            主题
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.RetainedMessageDto.Payload">
            <summary>
            消息负载
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.RetainedMessageDto.Qos">
            <summary>
            服务质量等级
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.RetainedMessageDto.RetainTime">
            <summary>
            保留时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.RetainedMessageDto.PayloadSize">
            <summary>
            负载大小（字节）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.RetainedMessageDto.Encoding">
            <summary>
            编码方式
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.RetainedMessageDto.PublisherId">
            <summary>
            发布者（如果可用）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.RetainedMessageDto.LastUpdateTime">
            <summary>
            最后更新时间
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.GetRetainedMessagesResponse">
            <summary>
            保留消息列表响应DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetRetainedMessagesResponse.Statistics">
            <summary>
            保留消息统计
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.RetainedMessageStatistics">
            <summary>
            保留消息统计DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.RetainedMessageStatistics.TotalRetainedMessages">
            <summary>
            总保留消息数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.RetainedMessageStatistics.TotalPayloadSize">
            <summary>
            总负载大小（字节）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.RetainedMessageStatistics.AveragePayloadSize">
            <summary>
            平均负载大小（字节）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.RetainedMessageStatistics.MaxPayloadSize">
            <summary>
            最大负载大小（字节）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.RetainedMessageStatistics.MinPayloadSize">
            <summary>
            最小负载大小（字节）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.RetainedMessageStatistics.UniqueTopics">
            <summary>
            唯一主题数
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.ClearRetainedMessageResponse">
            <summary>
            清除保留消息响应DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ClearRetainedMessageResponse.Topic">
            <summary>
            主题
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ClearRetainedMessageResponse.ClearTime">
            <summary>
            清除时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ClearRetainedMessageResponse.Success">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ClearRetainedMessageResponse.ErrorMessage">
            <summary>
            错误信息（失败时）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ClearRetainedMessageResponse.ClearedPayloadSize">
            <summary>
            清除的消息大小（字节）
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.BatchClearRetainedMessagesResponse">
            <summary>
            批量清除保留消息响应DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BatchClearRetainedMessagesResponse.TotalTopics">
            <summary>
            总主题数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BatchClearRetainedMessagesResponse.SuccessfulClears">
            <summary>
            成功清除的主题数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BatchClearRetainedMessagesResponse.FailedClears">
            <summary>
            失败的清除数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BatchClearRetainedMessagesResponse.ClearTime">
            <summary>
            清除时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BatchClearRetainedMessagesResponse.Results">
            <summary>
            详细结果
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BatchClearRetainedMessagesResponse.TotalClearedPayloadSize">
            <summary>
            总清除的负载大小（字节）
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.PayloadEncoding">
            <summary>
            负载编码方式
            </summary>
        </member>
        <member name="F:Admin.Application.MqttBrokerServices.Dto.PayloadEncoding.Utf8">
            <summary>
            UTF-8编码
            </summary>
        </member>
        <member name="F:Admin.Application.MqttBrokerServices.Dto.PayloadEncoding.Base64">
            <summary>
            Base64编码
            </summary>
        </member>
        <member name="F:Admin.Application.MqttBrokerServices.Dto.PayloadEncoding.Hex">
            <summary>
            十六进制编码
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.PublishStatus">
            <summary>
            发布状态
            </summary>
        </member>
        <member name="F:Admin.Application.MqttBrokerServices.Dto.PublishStatus.Success">
            <summary>
            成功
            </summary>
        </member>
        <member name="F:Admin.Application.MqttBrokerServices.Dto.PublishStatus.Failed">
            <summary>
            失败
            </summary>
        </member>
        <member name="F:Admin.Application.MqttBrokerServices.Dto.PublishStatus.PartialSuccess">
            <summary>
            部分成功
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.RetainedMessageSortBy">
            <summary>
            保留消息排序字段
            </summary>
        </member>
        <member name="F:Admin.Application.MqttBrokerServices.Dto.RetainedMessageSortBy.Topic">
            <summary>
            按主题排序
            </summary>
        </member>
        <member name="F:Admin.Application.MqttBrokerServices.Dto.RetainedMessageSortBy.RetainTime">
            <summary>
            按保留时间排序
            </summary>
        </member>
        <member name="F:Admin.Application.MqttBrokerServices.Dto.RetainedMessageSortBy.PayloadSize">
            <summary>
            按负载大小排序
            </summary>
        </member>
        <member name="F:Admin.Application.MqttBrokerServices.Dto.RetainedMessageSortBy.LastUpdateTime">
            <summary>
            按更新时间排序
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.SortDirection">
            <summary>
            排序方向
            </summary>
        </member>
        <member name="F:Admin.Application.MqttBrokerServices.Dto.SortDirection.Ascending">
            <summary>
            升序
            </summary>
        </member>
        <member name="F:Admin.Application.MqttBrokerServices.Dto.SortDirection.Descending">
            <summary>
            降序
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.MessagePublishResult">
            <summary>
            消息发布结果
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MessagePublishResult.IsSuccess">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MessagePublishResult.ErrorMessage">
            <summary>
            错误消息
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MessagePublishResult.Data">
            <summary>
            发布响应数据
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.BatchMessagePublishResult">
            <summary>
            批量消息发布结果
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BatchMessagePublishResult.IsSuccess">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BatchMessagePublishResult.ErrorMessage">
            <summary>
            错误消息
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BatchMessagePublishResult.Data">
            <summary>
            批量发布响应数据
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.RetainedMessageManagementResult">
            <summary>
            保留消息管理结果
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.RetainedMessageManagementResult.IsSuccess">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.RetainedMessageManagementResult.ErrorMessage">
            <summary>
            错误消息
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.RetainedMessageManagementResult.Data">
            <summary>
            数据
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.GetAclRulesInput">
            <summary>
            ACL规则查询输入
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetAclRulesInput.RuleName">
            <summary>
            规则名称
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetAclRulesInput.AccessType">
            <summary>
            访问类型
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetAclRulesInput.Username">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetAclRulesInput.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetAclRulesInput.Topic">
            <summary>
            主题
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetAclRulesInput.Permission">
            <summary>
            权限（allow/deny）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetAclRulesInput.IsActive">
            <summary>
            是否激活
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.AclRuleOutput">
            <summary>
            ACL规则输出
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclRuleOutput.Id">
            <summary>
            规则ID
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclRuleOutput.RuleName">
            <summary>
            规则名称
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclRuleOutput.Priority">
            <summary>
            优先级
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclRuleOutput.AccessType">
            <summary>
            访问类型
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclRuleOutput.Allow">
            <summary>
            是否允许访问
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclRuleOutput.Username">
            <summary>
            用户名（可选）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclRuleOutput.ClientId">
            <summary>
            客户端ID（可选）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclRuleOutput.IpAddress">
            <summary>
            IP地址（可选）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclRuleOutput.Topic">
            <summary>
            MQTT主题
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclRuleOutput.Description">
            <summary>
            描述
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclRuleOutput.IsActive">
            <summary>
            是否激活
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclRuleOutput.EffectiveStartTime">
            <summary>
            生效开始时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclRuleOutput.EffectiveEndTime">
            <summary>
            生效结束时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclRuleOutput.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclRuleOutput.UpdateTime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclRuleOutput.CreateBy">
            <summary>
            创建人
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclRuleOutput.UpdateBy">
            <summary>
            更新人
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AclRuleOutput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.AddAclRuleInput">
            <summary>
            添加ACL规则输入
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AddAclRuleInput.RuleName">
            <summary>
            规则名称
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AddAclRuleInput.Priority">
            <summary>
            优先级
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AddAclRuleInput.AccessType">
            <summary>
            访问类型
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AddAclRuleInput.Allow">
            <summary>
            是否允许访问
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AddAclRuleInput.Username">
            <summary>
            用户名（可选）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AddAclRuleInput.ClientId">
            <summary>
            客户端ID（可选）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AddAclRuleInput.IpAddress">
            <summary>
            IP地址（可选）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AddAclRuleInput.Topic">
            <summary>
            MQTT主题
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AddAclRuleInput.Description">
            <summary>
            描述
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AddAclRuleInput.IsActive">
            <summary>
            是否激活
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AddAclRuleInput.EffectiveStartTime">
            <summary>
            生效开始时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AddAclRuleInput.EffectiveEndTime">
            <summary>
            生效结束时间
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.PutAclRuleInput">
            <summary>
            更新ACL规则输入
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PutAclRuleInput.RuleName">
            <summary>
            规则名称
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PutAclRuleInput.Priority">
            <summary>
            优先级
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PutAclRuleInput.AccessType">
            <summary>
            访问类型
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PutAclRuleInput.Allow">
            <summary>
            是否允许访问
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PutAclRuleInput.Username">
            <summary>
            用户名（可选）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PutAclRuleInput.ClientId">
            <summary>
            客户端ID（可选）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PutAclRuleInput.IpAddress">
            <summary>
            IP地址（可选）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PutAclRuleInput.Topic">
            <summary>
            MQTT主题
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PutAclRuleInput.Description">
            <summary>
            描述
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PutAclRuleInput.IsActive">
            <summary>
            是否激活
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PutAclRuleInput.EffectiveStartTime">
            <summary>
            生效开始时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PutAclRuleInput.EffectiveEndTime">
            <summary>
            生效结束时间
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.TestPermissionInput">
            <summary>
            权限测试输入
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.TestPermissionInput.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.TestPermissionInput.Username">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.TestPermissionInput.Topic">
            <summary>
            主题
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.TestPermissionInput.AccessType">
            <summary>
            访问类型
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.PermissionTestOutput">
            <summary>
            权限测试输出
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PermissionTestOutput.IsAuthorized">
            <summary>
            是否授权
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PermissionTestOutput.FailureReason">
            <summary>
            失败原因
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PermissionTestOutput.MatchedRule">
            <summary>
            匹配的规则
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PermissionTestOutput.AllMatchedRules">
            <summary>
            所有匹配的规则
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PermissionTestOutput.TestTime">
            <summary>
            测试时间
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.MatchedRuleOutput">
            <summary>
            匹配的规则输出
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MatchedRuleOutput.Id">
            <summary>
            规则ID
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MatchedRuleOutput.Priority">
            <summary>
            优先级
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MatchedRuleOutput.Permission">
            <summary>
            权限
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MatchedRuleOutput.MatchedPattern">
            <summary>
            匹配的模式
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MatchedRuleOutput.MatchType">
            <summary>
            匹配类型
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.ImportResultOutput">
            <summary>
            导入结果输出
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ImportResultOutput.ImportedRules">
            <summary>
            导入成功的规则
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ImportResultOutput.FailedRules">
            <summary>
            导入失败的规则
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ImportResultOutput.TotalRequested">
            <summary>
            请求总数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ImportResultOutput.SuccessCount">
            <summary>
            成功数量
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ImportResultOutput.FailCount">
            <summary>
            失败数量
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ImportResultOutput.ImportTime">
            <summary>
            导入时间
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.GetRetainedMessagesInput">
            <summary>
            获取保留消息分页列表输入
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetRetainedMessagesInput.TopicPattern">
            <summary>
            主题模式（支持模糊搜索）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetRetainedMessagesInput.StartTime">
            <summary>
            开始时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetRetainedMessagesInput.EndTime">
            <summary>
            结束时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetRetainedMessagesInput.PublisherId">
            <summary>
            发布者客户端ID
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetRetainedMessagesInput.Qos">
            <summary>
            QoS等级
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.RetainedMessageOutput">
            <summary>
            保留消息输出
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.RetainedMessageOutput.Topic">
            <summary>
            主题
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.RetainedMessageOutput.Payload">
            <summary>
            消息负载
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.RetainedMessageOutput.Qos">
            <summary>
            服务质量等级
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.RetainedMessageOutput.RetainTime">
            <summary>
            消息保留时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.RetainedMessageOutput.LastUpdateTime">
            <summary>
            最后更新时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.RetainedMessageOutput.PayloadSize">
            <summary>
            负载大小（字节）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.RetainedMessageOutput.Encoding">
            <summary>
            负载编码方式
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.RetainedMessageOutput.PublisherId">
            <summary>
            发布者客户端ID
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.RetainedMessageOutput.Id">
            <summary>
            消息ID
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.ClearRetainedMessageInput">
            <summary>
            清除保留消息输入
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ClearRetainedMessageInput.Topic">
            <summary>
            要清除的主题
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.ClearRetainedMessageOutput">
            <summary>
            清除保留消息输出
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ClearRetainedMessageOutput.Topic">
            <summary>
            主题
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ClearRetainedMessageOutput.ClearTime">
            <summary>
            清除时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ClearRetainedMessageOutput.Success">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ClearRetainedMessageOutput.ClearedPayloadSize">
            <summary>
            清除的负载大小
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ClearRetainedMessageOutput.ErrorMessage">
            <summary>
            错误信息（如果有）
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.AddMqttUserInput">
            <summary>
            添加MQTT用户请求DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AddMqttUserInput.Username">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AddMqttUserInput.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AddMqttUserInput.ClientIdLimit">
            <summary>
            客户端ID限制，多个用逗号分隔，为空表示不限制
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AddMqttUserInput.ExpireTime">
            <summary>
            过期时间，为空表示永不过期
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AddMqttUserInput.Description">
            <summary>
            描述
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AddMqttUserInput.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.UpdateMqttUserInput">
            <summary>
            更新MQTT用户请求DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.UpdateMqttUserInput.Password">
            <summary>
            密码（为空表示不修改密码）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.UpdateMqttUserInput.ClientIdLimit">
            <summary>
            客户端ID限制，多个用逗号分隔，为空表示不限制
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.UpdateMqttUserInput.ExpireTime">
            <summary>
            过期时间，为空表示永不过期
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.UpdateMqttUserInput.Description">
            <summary>
            描述
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.UpdateMqttUserInput.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.MqttUserQueryInput">
            <summary>
            MQTT用户查询请求DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MqttUserQueryInput.Username">
            <summary>
            用户名过滤
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MqttUserQueryInput.IsEnabled">
            <summary>
            是否启用过滤
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.CreateDeviceMqttUserInput">
            <summary>
            创建设备MQTT用户请求DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.CreateDeviceMqttUserInput.DeviceId">
            <summary>
            设备ID（作为用户名）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.CreateDeviceMqttUserInput.DeviceSecret">
            <summary>
            设备密钥（作为密码）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.CreateDeviceMqttUserInput.DeviceName">
            <summary>
            设备名称（作为描述）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.CreateDeviceMqttUserInput.ExpireTime">
            <summary>
            过期时间，为空表示永不过期
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.MqttUserOutput">
            <summary>
            MQTT用户输出DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MqttUserOutput.Username">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MqttUserOutput.ClientIdLimit">
            <summary>
            客户端ID限制
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MqttUserOutput.ExpireTime">
            <summary>
            过期时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MqttUserOutput.LastLoginTime">
            <summary>
            最后登录时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MqttUserOutput.LastLoginIp">
            <summary>
            最后登录IP
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MqttUserOutput.Description">
            <summary>
            描述
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MqttUserOutput.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MqttUserOutput.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MqttUserOutput.IsExpired">
            <summary>
            是否过期
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MqttUserOutput.StatusDescription">
            <summary>
            状态描述
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.SessionInfoDto">
            <summary>
            会话信息DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionInfoDto.SessionId">
            <summary>
            会话ID
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionInfoDto.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionInfoDto.CleanSession">
            <summary>
            是否为清除会话
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionInfoDto.State">
            <summary>
            会话状态
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionInfoDto.CreatedTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionInfoDto.LastActivityTime">
            <summary>
            最后活动时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionInfoDto.ExpiryTime">
            <summary>
            过期时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionInfoDto.SubscriptionCount">
            <summary>
            订阅数量
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionInfoDto.PendingMessageCount">
            <summary>
            待发送消息数量
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionInfoDto.AwaitingAckCount">
            <summary>
            待确认消息数量
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionInfoDto.AwaitingCompCount">
            <summary>
            待完成消息数量
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionInfoDto.Duration">
            <summary>
            会话持续时间(格式化字符串)
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionInfoDto.IsExpired">
            <summary>
            是否过期
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionInfoDto.IsPersistent">
            <summary>
            是否为持久会话
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.SessionDetailDto">
            <summary>
            会话详情DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionDetailDto.Subscriptions">
            <summary>
            订阅详情列表
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionDetailDto.PendingMessages">
            <summary>
            待发送消息列表
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionDetailDto.Statistics">
            <summary>
            会话统计信息
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.SessionSubscriptionDto">
            <summary>
            会话订阅信息DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionSubscriptionDto.TopicFilter">
            <summary>
            主题过滤器
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionSubscriptionDto.Qos">
            <summary>
            服务质量等级
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionSubscriptionDto.SubscribedTime">
            <summary>
            订阅时间
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.PendingMessageDto">
            <summary>
            待发送消息DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PendingMessageDto.MessageId">
            <summary>
            消息ID
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PendingMessageDto.Topic">
            <summary>
            主题
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PendingMessageDto.Qos">
            <summary>
            服务质量等级
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PendingMessageDto.Payload">
            <summary>
            消息载荷(Base64编码)
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PendingMessageDto.EnqueuedTime">
            <summary>
            入队时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PendingMessageDto.PayloadSize">
            <summary>
            消息大小(字节)
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.SessionStatisticsDto">
            <summary>
            会话统计信息DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionStatisticsDto.SubscriptionCount">
            <summary>
            订阅数量
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionStatisticsDto.PendingMessageCount">
            <summary>
            待发送消息数量
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionStatisticsDto.SentMessageCount">
            <summary>
            已发送消息数量
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionStatisticsDto.ReceivedMessageCount">
            <summary>
            已接收消息数量
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionStatisticsDto.FailedMessageCount">
            <summary>
            消息发送失败数量
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionStatisticsDto.LastSentMessageTime">
            <summary>
            最后发送消息时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionStatisticsDto.LastReceivedMessageTime">
            <summary>
            最后接收消息时间
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.GetSessionsRequest">
            <summary>
            会话查询请求DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetSessionsRequest.ClientId">
            <summary>
            客户端ID过滤
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetSessionsRequest.State">
            <summary>
            会话状态过滤
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetSessionsRequest.Persistent">
            <summary>
            是否持久会话过滤
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetSessionsRequest.Expired">
            <summary>
            是否过期过滤
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.GetSessionsResponse">
            <summary>
            会话列表响应DTO
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.UpdateSessionStateRequest">
            <summary>
            会话状态更新请求DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.UpdateSessionStateRequest.Action">
            <summary>
            操作类型
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.UpdateSessionStateRequest.Reason">
            <summary>
            操作原因
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.UpdateSessionStateResponse">
            <summary>
            会话状态更新响应DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.UpdateSessionStateResponse.SessionId">
            <summary>
            会话ID
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.UpdateSessionStateResponse.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.UpdateSessionStateResponse.OldState">
            <summary>
            原状态
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.UpdateSessionStateResponse.NewState">
            <summary>
            新状态
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.UpdateSessionStateResponse.UpdateTime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.UpdateSessionStateResponse.Reason">
            <summary>
            操作原因
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.CleanupSessionResponse">
            <summary>
            会话清理响应DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.CleanupSessionResponse.SessionId">
            <summary>
            会话ID
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.CleanupSessionResponse.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.CleanupSessionResponse.CleanupTime">
            <summary>
            清理时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.CleanupSessionResponse.DeletedSubscriptions">
            <summary>
            删除的订阅数量
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.CleanupSessionResponse.DeletedPendingMessages">
            <summary>
            删除的待发送消息数量
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.CleanupSessionResponse.DeletedAwaitingAckMessages">
            <summary>
            删除的待确认消息数量
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.CleanupSessionResponse.DeletedAwaitingCompMessages">
            <summary>
            删除的待完成消息数量
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.BatchSessionOperationRequest">
            <summary>
            批量会话操作请求DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BatchSessionOperationRequest.SessionIds">
            <summary>
            会话ID列表
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BatchSessionOperationRequest.Operation">
            <summary>
            操作类型
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BatchSessionOperationRequest.Reason">
            <summary>
            操作原因
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.SessionOperationResult">
            <summary>
            批量会话操作结果DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionOperationResult.SessionId">
            <summary>
            会话ID
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionOperationResult.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionOperationResult.Success">
            <summary>
            操作是否成功
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionOperationResult.OperationTime">
            <summary>
            操作时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionOperationResult.Error">
            <summary>
            错误信息
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.BatchSessionOperationResponse">
            <summary>
            批量会话操作响应DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BatchSessionOperationResponse.TotalRequested">
            <summary>
            请求操作的总数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BatchSessionOperationResponse.SuccessfulOperations">
            <summary>
            成功操作的数量
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BatchSessionOperationResponse.FailedOperations">
            <summary>
            失败操作的数量
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BatchSessionOperationResponse.Results">
            <summary>
            操作结果列表
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.SessionManagementResult">
            <summary>
            会话管理操作结果
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionManagementResult.IsSuccess">
            <summary>
            操作是否成功
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionManagementResult.Message">
            <summary>
            消息
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionManagementResult.Data">
            <summary>
            返回数据
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionManagementResult.ErrorMessage">
            <summary>
            错误信息
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionManagementResult.Timestamp">
            <summary>
            时间戳
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.Dto.SessionManagementResult.Success(System.String,System.Object)">
            <summary>
            创建成功结果
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.Dto.SessionManagementResult.Error(System.String)">
            <summary>
            创建失败结果
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.StatisticsOverviewDto">
            <summary>
            总体统计概览DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.StatisticsOverviewDto.Broker">
            <summary>
            代理服务信息
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.StatisticsOverviewDto.Connections">
            <summary>
            连接统计
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.StatisticsOverviewDto.Sessions">
            <summary>
            会话统计
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.StatisticsOverviewDto.Messages">
            <summary>
            消息统计
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.StatisticsOverviewDto.Subscriptions">
            <summary>
            订阅统计
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.StatisticsOverviewDto.Authentication">
            <summary>
            认证统计
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.BrokerStatisticsDto">
            <summary>
            代理服务统计DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerStatisticsDto.IsRunning">
            <summary>
            是否运行中
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerStatisticsDto.Uptime">
            <summary>
            运行时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerStatisticsDto.Version">
            <summary>
            版本信息
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerStatisticsDto.StartTime">
            <summary>
            启动时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.BrokerStatisticsDto.Port">
            <summary>
            监听端口
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.ConnectionStatisticsDto">
            <summary>
            连接统计DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ConnectionStatisticsDto.Current">
            <summary>
            当前连接数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ConnectionStatisticsDto.Total">
            <summary>
            总连接数（累计）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ConnectionStatisticsDto.TotalDisconnections">
            <summary>
            总断开连接数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ConnectionStatisticsDto.AbnormalDisconnections">
            <summary>
            异常断开连接数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ConnectionStatisticsDto.MaxConcurrent">
            <summary>
            最大并发连接数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ConnectionStatisticsDto.AverageDuration">
            <summary>
            平均连接持续时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ConnectionStatisticsDto.ConnectionsByIp">
            <summary>
            按IP分组的连接数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ConnectionStatisticsDto.TopClientsByConnections">
            <summary>
            连接数最多的客户端
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.SessionStatisticsOverviewDto">
            <summary>
            会话统计概览DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionStatisticsOverviewDto.Active">
            <summary>
            活跃会话数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionStatisticsOverviewDto.Suspended">
            <summary>
            暂停会话数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionStatisticsOverviewDto.Persistent">
            <summary>
            持久会话数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionStatisticsOverviewDto.TotalCreated">
            <summary>
            总创建会话数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionStatisticsOverviewDto.Expired">
            <summary>
            过期会话数
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.MessageStatisticsDto">
            <summary>
            消息统计DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MessageStatisticsDto.TotalPublished">
            <summary>
            总发布消息数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MessageStatisticsDto.TotalReceived">
            <summary>
            总接收消息数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MessageStatisticsDto.TotalBytesTransferred">
            <summary>
            总传输字节数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MessageStatisticsDto.PublishRate">
            <summary>
            发布速率（消息/秒）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MessageStatisticsDto.ReceiveRate">
            <summary>
            接收速率（消息/秒）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MessageStatisticsDto.AverageMessageSize">
            <summary>
            平均消息大小（字节）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MessageStatisticsDto.TopTopicsByMessages">
            <summary>
            消息数最多的主题
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MessageStatisticsDto.MessagesByQos">
            <summary>
            按QoS分组的消息统计
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MessageStatisticsDto.RetainedMessageCount">
            <summary>
            保留消息数
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.SubscriptionStatisticsDto">
            <summary>
            订阅统计DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SubscriptionStatisticsDto.TotalSubscriptions">
            <summary>
            总订阅数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SubscriptionStatisticsDto.UniqueTopics">
            <summary>
            唯一主题数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SubscriptionStatisticsDto.AverageSubscriptionsPerClient">
            <summary>
            平均每客户端订阅数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SubscriptionStatisticsDto.TopSubscribedTopics">
            <summary>
            订阅数最多的主题
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SubscriptionStatisticsDto.WildcardSubscriptions">
            <summary>
            通配符订阅数
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.AuthenticationStatisticsDto">
            <summary>
            认证统计DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AuthenticationStatisticsDto.TotalAttempts">
            <summary>
            总认证尝试数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AuthenticationStatisticsDto.SuccessfulAttempts">
            <summary>
            成功认证数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AuthenticationStatisticsDto.FailedAttempts">
            <summary>
            失败认证数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AuthenticationStatisticsDto.SuccessRate">
            <summary>
            成功率（百分比）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AuthenticationStatisticsDto.RecentFailures">
            <summary>
            最近认证失败记录
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.PerformanceMetricsDto">
            <summary>
            性能指标DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PerformanceMetricsDto.Timestamp">
            <summary>
            时间戳
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PerformanceMetricsDto.CpuUsage">
            <summary>
            CPU使用率（百分比）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PerformanceMetricsDto.MemoryUsage">
            <summary>
            内存使用情况
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PerformanceMetricsDto.NetworkMetrics">
            <summary>
            网络指标
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PerformanceMetricsDto.SystemMetrics">
            <summary>
            系统指标
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PerformanceMetricsDto.HistoricalData">
            <summary>
            历史数据
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.MemoryUsageDto">
            <summary>
            内存使用DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MemoryUsageDto.WorkingSet">
            <summary>
            工作集内存（字节）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MemoryUsageDto.PrivateMemory">
            <summary>
            私有内存（字节）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MemoryUsageDto.VirtualMemory">
            <summary>
            虚拟内存（字节）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MemoryUsageDto.GcTotalMemory">
            <summary>
            GC总内存（字节）
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.NetworkMetricsDto">
            <summary>
            网络指标DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.NetworkMetricsDto.ConnectionsPerSecond">
            <summary>
            每秒连接数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.NetworkMetricsDto.MessagesPerSecond">
            <summary>
            每秒消息数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.NetworkMetricsDto.BytesPerSecond">
            <summary>
            每秒字节数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.NetworkMetricsDto.ActiveConnections">
            <summary>
            活跃连接数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.NetworkMetricsDto.PacketLoss">
            <summary>
            丢包率（百分比）
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.SystemMetricsDto">
            <summary>
            系统指标DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SystemMetricsDto.ThreadCount">
            <summary>
            线程数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SystemMetricsDto.HandleCount">
            <summary>
            句柄数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SystemMetricsDto.UptimeSeconds">
            <summary>
            运行时间（秒）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SystemMetricsDto.GcCollections">
            <summary>
            GC收集次数
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.TimeSeriesDataDto">
            <summary>
            时间序列数据DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.TimeSeriesDataDto.Timestamp">
            <summary>
            时间戳
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.TimeSeriesDataDto.Value">
            <summary>
            数值
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.TimeSeriesDataDto.Label">
            <summary>
            标签
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.TopClientDto">
            <summary>
            顶级客户端DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.TopClientDto.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.TopClientDto.Count">
            <summary>
            连接数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.TopClientDto.LastSeen">
            <summary>
            最后连接时间
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.TopTopicDto">
            <summary>
            顶级主题DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.TopTopicDto.Topic">
            <summary>
            主题名称
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.TopTopicDto.MessageCount">
            <summary>
            消息数量
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.TopTopicDto.LastActivity">
            <summary>
            最后活动时间
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.MessageQosStatsDto">
            <summary>
            消息QoS统计DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MessageQosStatsDto.QosLevel">
            <summary>
            QoS级别
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MessageQosStatsDto.Count">
            <summary>
            消息数量
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MessageQosStatsDto.Percentage">
            <summary>
            百分比
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.TopSubscribedTopicDto">
            <summary>
            顶级订阅主题DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.TopSubscribedTopicDto.Topic">
            <summary>
            主题名称
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.TopSubscribedTopicDto.SubscriberCount">
            <summary>
            订阅者数量
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.TopSubscribedTopicDto.Percentage">
            <summary>
            百分比
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.AuthenticationFailureDto">
            <summary>
            认证失败DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AuthenticationFailureDto.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AuthenticationFailureDto.IpAddress">
            <summary>
            IP地址
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AuthenticationFailureDto.FailureTime">
            <summary>
            失败时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.AuthenticationFailureDto.Reason">
            <summary>
            失败原因
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.GetPerformanceMetricsRequest">
            <summary>
            获取性能指标请求DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetPerformanceMetricsRequest.StartTime">
            <summary>
            开始时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetPerformanceMetricsRequest.EndTime">
            <summary>
            结束时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetPerformanceMetricsRequest.Interval">
            <summary>
            时间间隔
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.GetConnectionStatisticsRequest">
            <summary>
            获取连接统计请求DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetConnectionStatisticsRequest.StartTime">
            <summary>
            开始时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetConnectionStatisticsRequest.EndTime">
            <summary>
            结束时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetConnectionStatisticsRequest.GroupBy">
            <summary>
            时间分组方式
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.GetConnectionStatisticsResponse">
            <summary>
            获取连接统计响应DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetConnectionStatisticsResponse.Summary">
            <summary>
            连接统计摘要
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetConnectionStatisticsResponse.ConnectionsByIp">
            <summary>
            按IP分组的连接统计
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetConnectionStatisticsResponse.ConnectionsByTime">
            <summary>
            按时间分组的连接统计
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetConnectionStatisticsResponse.TopClients">
            <summary>
            顶级客户端
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetConnectionStatisticsResponse.ConnectionDurationDistribution">
            <summary>
            连接持续时间分布
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.ConnectionStatisticsSummaryDto">
            <summary>
            连接统计摘要DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ConnectionStatisticsSummaryDto.CurrentConnections">
            <summary>
            当前连接数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ConnectionStatisticsSummaryDto.TotalConnections">
            <summary>
            总连接数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ConnectionStatisticsSummaryDto.TotalDisconnections">
            <summary>
            总断开连接数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ConnectionStatisticsSummaryDto.AbnormalDisconnections">
            <summary>
            异常断开连接数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ConnectionStatisticsSummaryDto.MaxConcurrentConnections">
            <summary>
            最大并发连接数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ConnectionStatisticsSummaryDto.AverageConnectionDuration">
            <summary>
            平均连接持续时间（秒）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ConnectionStatisticsSummaryDto.ConnectionSuccessRate">
            <summary>
            连接成功率（百分比）
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.IpConnectionStatsDto">
            <summary>
            IP连接统计DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.IpConnectionStatsDto.IpAddress">
            <summary>
            IP地址
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.IpConnectionStatsDto.ConnectionCount">
            <summary>
            连接数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.IpConnectionStatsDto.Percentage">
            <summary>
            百分比
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.ConnectionDurationStatsDto">
            <summary>
            连接持续时间统计DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ConnectionDurationStatsDto.Range">
            <summary>
            时间范围
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ConnectionDurationStatsDto.Count">
            <summary>
            连接数
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.GetMessageStatisticsRequest">
            <summary>
            获取消息统计请求DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetMessageStatisticsRequest.StartTime">
            <summary>
            开始时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetMessageStatisticsRequest.EndTime">
            <summary>
            结束时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetMessageStatisticsRequest.GroupBy">
            <summary>
            时间分组方式
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetMessageStatisticsRequest.TopTopicsCount">
            <summary>
            顶级主题数量
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.GetMessageStatisticsResponse">
            <summary>
            获取消息统计响应DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetMessageStatisticsResponse.Summary">
            <summary>
            消息统计摘要
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetMessageStatisticsResponse.MessagesByQos">
            <summary>
            按QoS分组的消息统计
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetMessageStatisticsResponse.MessagesByTopic">
            <summary>
            按主题分组的消息统计
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetMessageStatisticsResponse.MessagesByTime">
            <summary>
            按时间分组的消息统计
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetMessageStatisticsResponse.RetainedMessages">
            <summary>
            保留消息统计
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.MessageStatisticsSummaryDto">
            <summary>
            消息统计摘要DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MessageStatisticsSummaryDto.TotalMessages">
            <summary>
            总消息数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MessageStatisticsSummaryDto.TotalPublished">
            <summary>
            总发布消息数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MessageStatisticsSummaryDto.TotalReceived">
            <summary>
            总接收消息数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MessageStatisticsSummaryDto.TotalBytesTransferred">
            <summary>
            总传输字节数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MessageStatisticsSummaryDto.AverageMessageSize">
            <summary>
            平均消息大小
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MessageStatisticsSummaryDto.MessagesPerSecond">
            <summary>
            每秒消息数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.MessageStatisticsSummaryDto.PublishSuccessRate">
            <summary>
            发布成功率（百分比）
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.RetainedMessageStatsDto">
            <summary>
            保留消息统计DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.RetainedMessageStatsDto.Count">
            <summary>
            保留消息数量
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.RetainedMessageStatsDto.TotalSize">
            <summary>
            总大小（字节）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.RetainedMessageStatsDto.TopTopics">
            <summary>
            顶级主题
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.GetTopicAnalysisRequest">
            <summary>
            获取主题分析请求DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetTopicAnalysisRequest.TopTopicsCount">
            <summary>
            顶级主题数量
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetTopicAnalysisRequest.MaxDepth">
            <summary>
            最大深度
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetTopicAnalysisRequest.TopicFilter">
            <summary>
            主题过滤器
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.GetTopicAnalysisResponse">
            <summary>
            获取主题分析响应DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetTopicAnalysisResponse.Summary">
            <summary>
            主题分析摘要
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetTopicAnalysisResponse.TopTopicsBySubscribers">
            <summary>
            订阅者最多的主题
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetTopicAnalysisResponse.TopTopicsByMessages">
            <summary>
            消息最多的主题
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetTopicAnalysisResponse.TopicHierarchy">
            <summary>
            主题层次结构
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetTopicAnalysisResponse.WildcardUsage">
            <summary>
            通配符使用情况
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetTopicAnalysisResponse.TopicDetails">
            <summary>
            主题详细信息
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.TopicAnalysisSummaryDto">
            <summary>
            主题分析摘要DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.TopicAnalysisSummaryDto.TotalTopics">
            <summary>
            总主题数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.TopicAnalysisSummaryDto.ActiveTopics">
            <summary>
            活跃主题数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.TopicAnalysisSummaryDto.SubscribedTopics">
            <summary>
            已订阅主题数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.TopicAnalysisSummaryDto.AverageSubscribersPerTopic">
            <summary>
            平均每主题订阅者数
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.TopicInfoDto">
            <summary>
            主题信息DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.TopicInfoDto.Topic">
            <summary>
            主题名称
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.TopicInfoDto.SubscriberCount">
            <summary>
            订阅者数量
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.TopicInfoDto.MessageCount">
            <summary>
            消息数量
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.TopicInfoDto.LastActivity">
            <summary>
            最后活动时间
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.TopicHierarchyDto">
            <summary>
            主题层次结构DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.TopicHierarchyDto.MaxDepth">
            <summary>
            最大深度
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.TopicHierarchyDto.TotalLevels">
            <summary>
            总层级数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.TopicHierarchyDto.Hierarchy">
            <summary>
            层次结构
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.TopicLevelDto">
            <summary>
            主题层级DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.TopicLevelDto.Level">
            <summary>
            层级
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.TopicLevelDto.TopicCount">
            <summary>
            主题数量
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.TopicLevelDto.Topics">
            <summary>
            主题列表
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.WildcardUsageDto">
            <summary>
            通配符使用情况DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.WildcardUsageDto.TotalWildcardTopics">
            <summary>
            总通配符主题数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.WildcardUsageDto.SingleLevelWildcards">
            <summary>
            单级通配符数量
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.WildcardUsageDto.MultiLevelWildcards">
            <summary>
            多级通配符数量
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.WildcardUsageDto.WildcardPercentage">
            <summary>
            通配符百分比
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.TopicDetailDto">
            <summary>
            主题详细信息DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.TopicDetailDto.Topic">
            <summary>
            主题名称
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.TopicDetailDto.SubscriberCount">
            <summary>
            订阅者数量
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.TopicDetailDto.MessageCount">
            <summary>
            消息数量
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.TopicDetailDto.TotalBytes">
            <summary>
            总字节数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.TopicDetailDto.LastActivity">
            <summary>
            最后活动时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.TopicDetailDto.Subscribers">
            <summary>
            订阅者列表
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.GetSessionStatisticsRequest">
            <summary>
            获取会话统计请求DTO
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.GetSessionStatisticsResponse">
            <summary>
            获取会话统计响应DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetSessionStatisticsResponse.Summary">
            <summary>
            会话统计摘要
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetSessionStatisticsResponse.SessionsByState">
            <summary>
            按状态分组的会话统计
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetSessionStatisticsResponse.SessionsByDuration">
            <summary>
            按持续时间分组的会话统计
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetSessionStatisticsResponse.PendingMessagesStats">
            <summary>
            待发送消息统计
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.SessionStatisticsSummaryDto">
            <summary>
            会话统计摘要DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionStatisticsSummaryDto.TotalSessions">
            <summary>
            总会话数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionStatisticsSummaryDto.ActiveSessions">
            <summary>
            活跃会话数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionStatisticsSummaryDto.SuspendedSessions">
            <summary>
            暂停会话数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionStatisticsSummaryDto.PersistentSessions">
            <summary>
            持久会话数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionStatisticsSummaryDto.CleanSessions">
            <summary>
            清理会话数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionStatisticsSummaryDto.AverageSessionDuration">
            <summary>
            平均会话持续时间（秒）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionStatisticsSummaryDto.SessionsWithPendingMessages">
            <summary>
            有待发送消息的会话数
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.SessionStateStatsDto">
            <summary>
            会话状态统计DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionStateStatsDto.State">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionStateStatsDto.Count">
            <summary>
            数量
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionStateStatsDto.Percentage">
            <summary>
            百分比
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.SessionDurationStatsDto">
            <summary>
            会话持续时间统计DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionDurationStatsDto.Range">
            <summary>
            时间范围
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.SessionDurationStatsDto.Count">
            <summary>
            会话数
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.PendingMessagesStatsDto">
            <summary>
            待发送消息统计DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PendingMessagesStatsDto.TotalPendingMessages">
            <summary>
            总待发送消息数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PendingMessagesStatsDto.SessionsWithPendingMessages">
            <summary>
            有待发送消息的会话数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PendingMessagesStatsDto.AveragePendingMessagesPerSession">
            <summary>
            平均每会话待发送消息数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.PendingMessagesStatsDto.MaxPendingMessagesInSession">
            <summary>
            单个会话最大待发送消息数
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.GetHistoricalStatisticsRequest">
            <summary>
            获取历史统计请求DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetHistoricalStatisticsRequest.StartTime">
            <summary>
            开始时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetHistoricalStatisticsRequest.EndTime">
            <summary>
            结束时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetHistoricalStatisticsRequest.Interval">
            <summary>
            时间间隔
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.GetHistoricalStatisticsResponse">
            <summary>
            获取历史统计响应DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetHistoricalStatisticsResponse.TimeRange">
            <summary>
            时间范围
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetHistoricalStatisticsResponse.ConnectionHistory">
            <summary>
            连接历史数据
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetHistoricalStatisticsResponse.MessageHistory">
            <summary>
            消息历史数据
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetHistoricalStatisticsResponse.PerformanceHistory">
            <summary>
            性能历史数据
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.GetHistoricalStatisticsResponse.Summary">
            <summary>
            历史摘要
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.TimeRangeDto">
            <summary>
            时间范围DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.TimeRangeDto.StartTime">
            <summary>
            开始时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.TimeRangeDto.EndTime">
            <summary>
            结束时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.TimeRangeDto.Interval">
            <summary>
            时间间隔
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.HistoricalSummaryDto">
            <summary>
            历史摘要DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.HistoricalSummaryDto.PeakConnections">
            <summary>
            峰值连接数
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.HistoricalSummaryDto.PeakMessageRate">
            <summary>
            峰值消息速率
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.HistoricalSummaryDto.AverageUptime">
            <summary>
            平均运行时间（小时）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.HistoricalSummaryDto.TotalDowntime">
            <summary>
            总停机时间
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.ExportStatisticsRequest">
            <summary>
            导出统计请求DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ExportStatisticsRequest.Format">
            <summary>
            导出格式
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ExportStatisticsRequest.StartTime">
            <summary>
            开始时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ExportStatisticsRequest.EndTime">
            <summary>
            结束时间
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ExportStatisticsRequest.IncludeTypes">
            <summary>
            包含的统计类型
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.ExportStatisticsResponse">
            <summary>
            导出统计响应DTO
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ExportStatisticsResponse.FileName">
            <summary>
            文件名
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ExportStatisticsResponse.ContentType">
            <summary>
            内容类型
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ExportStatisticsResponse.FileSize">
            <summary>
            文件大小（字节）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ExportStatisticsResponse.Data">
            <summary>
            文件数据（Base64编码）
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.ExportStatisticsResponse.GeneratedAt">
            <summary>
            生成时间
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.TimeGroupBy">
            <summary>
            时间分组方式
            </summary>
        </member>
        <member name="F:Admin.Application.MqttBrokerServices.Dto.TimeGroupBy.Minute">
            <summary>
            按分钟
            </summary>
        </member>
        <member name="F:Admin.Application.MqttBrokerServices.Dto.TimeGroupBy.Hour">
            <summary>
            按小时
            </summary>
        </member>
        <member name="F:Admin.Application.MqttBrokerServices.Dto.TimeGroupBy.Day">
            <summary>
            按天
            </summary>
        </member>
        <member name="F:Admin.Application.MqttBrokerServices.Dto.TimeGroupBy.Week">
            <summary>
            按周
            </summary>
        </member>
        <member name="F:Admin.Application.MqttBrokerServices.Dto.TimeGroupBy.Month">
            <summary>
            按月
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.ExportFormat">
            <summary>
            导出格式
            </summary>
        </member>
        <member name="F:Admin.Application.MqttBrokerServices.Dto.ExportFormat.Json">
            <summary>
            JSON格式
            </summary>
        </member>
        <member name="F:Admin.Application.MqttBrokerServices.Dto.ExportFormat.Csv">
            <summary>
            CSV格式
            </summary>
        </member>
        <member name="F:Admin.Application.MqttBrokerServices.Dto.ExportFormat.Excel">
            <summary>
            Excel格式
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.StatisticsType">
            <summary>
            统计类型
            </summary>
        </member>
        <member name="F:Admin.Application.MqttBrokerServices.Dto.StatisticsType.Connection">
            <summary>
            连接统计
            </summary>
        </member>
        <member name="F:Admin.Application.MqttBrokerServices.Dto.StatisticsType.Message">
            <summary>
            消息统计
            </summary>
        </member>
        <member name="F:Admin.Application.MqttBrokerServices.Dto.StatisticsType.Session">
            <summary>
            会话统计
            </summary>
        </member>
        <member name="F:Admin.Application.MqttBrokerServices.Dto.StatisticsType.Topic">
            <summary>
            主题统计
            </summary>
        </member>
        <member name="F:Admin.Application.MqttBrokerServices.Dto.StatisticsType.Performance">
            <summary>
            性能统计
            </summary>
        </member>
        <member name="F:Admin.Application.MqttBrokerServices.Dto.StatisticsType.Authentication">
            <summary>
            认证统计
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Dto.StatisticsResult">
            <summary>
            统计结果封装
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.StatisticsResult.IsSuccess">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.StatisticsResult.Message">
            <summary>
            消息
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.StatisticsResult.Data">
            <summary>
            数据
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.StatisticsResult.ErrorMessage">
            <summary>
            错误信息
            </summary>
        </member>
        <member name="P:Admin.Application.MqttBrokerServices.Dto.StatisticsResult.Timestamp">
            <summary>
            时间戳
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.Dto.StatisticsResult.Success(System.String,System.Object)">
            <summary>
            成功结果
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.Dto.StatisticsResult.Error(System.String)">
            <summary>
            错误结果
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Mapper.MqttAclMapperProfile">
            <summary>
            MQTT ACL映射配置
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.Mapper.MqttUserMapperProfile">
            <summary>
            MQTT用户映射配置
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.IMqttAclManagementService">
            <summary>
            MQTT ACL权限管理服务接口
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.IMqttAclManagementService.GetPagedListAsync(Admin.Application.MqttBrokerServices.Dto.GetAclRulesInput)">
            <summary>
            获取ACL规则列表
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.IMqttAclManagementService.GetAsync(System.Int64)">
            <summary>
            获取单个ACL规则详情
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.IMqttAclManagementService.AddAsync(Admin.Application.MqttBrokerServices.Dto.AddAclRuleInput)">
            <summary>
            添加ACL规则
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.IMqttAclManagementService.PutAsync(System.Int64,Admin.Application.MqttBrokerServices.Dto.PutAclRuleInput)">
            <summary>
            更新ACL规则
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.IMqttAclManagementService.DeleteAsync(System.Int64)">
            <summary>
            删除ACL规则
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.IMqttAclManagementService.BatchDeleteAsync(System.Collections.Generic.List{System.Int64})">
            <summary>
            批量删除ACL规则
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.IMqttAclManagementService.TestPermissionAsync(Admin.Application.MqttBrokerServices.Dto.TestPermissionInput)">
            <summary>
            测试权限
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.IMqttAclManagementService.ToggleAsync(System.Int64,System.Boolean)">
            <summary>
            启用/禁用ACL规则
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.IMqttAclManagementService.ImportAsync(System.Collections.Generic.List{Admin.Application.MqttBrokerServices.Dto.AddAclRuleInput})">
            <summary>
            导入ACL规则
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.IMqttAclManagementService.ExportAsync">
            <summary>
            导出ACL规则
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.IMqttAclManagementService.CreateDeviceAclRulesAsync(Admin.Application.MqttBrokerServices.Dto.CreateDeviceAclRulesInput)">
            <summary>
            为设备创建ACL规则
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.IMqttAclManagementService.DeleteDeviceAclRulesAsync(Admin.Application.MqttBrokerServices.Dto.DeleteDeviceAclRulesInput)">
            <summary>
            删除设备的ACL规则
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.IMqttAclManagementService.QueryDeviceAclRulesAsync(Admin.Application.MqttBrokerServices.Dto.QueryDeviceAclRulesInput)">
            <summary>
            查询设备的ACL规则
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.IMqttAclManagementService.UpdateDeviceAclRulesAsync(System.String,Admin.Application.MqttBrokerServices.Dto.CreateDeviceAclRulesInput)">
            <summary>
            更新设备ACL规则（当设备信息变更时）
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.MqttAclManagementService">
            <summary>
            MQTT ACL权限管理服务
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttAclManagementService.#ctor(SqlSugar.ISqlSugarClient,Admin.SqlSugar.Repository{Admin.SqlSugar.Entity.Business.LOT.MqttAclRuleEntity},Admin.Communication.Mqtt.Abstractions.IMqttBroker,Admin.Communication.Mqtt.Abstractions.IMqttConnectionManager,Microsoft.Extensions.Logging.ILogger{Admin.Application.MqttBrokerServices.MqttAclManagementService},Microsoft.Extensions.Options.IOptions{Admin.Communication.Mqtt.Configuration.MqttBrokerOptions})">
            <summary>
            MQTT ACL权限管理服务
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttAclManagementService.GetPagedListAsync(Admin.Application.MqttBrokerServices.Dto.GetAclRulesInput)">
            <summary>
            分页查询
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttAclManagementService.GetAsync(System.Int64)">
            <summary>
            单条查询
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttAclManagementService.AddAsync(Admin.Application.MqttBrokerServices.Dto.AddAclRuleInput)">
            <summary>
            添加
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttAclManagementService.PutAsync(System.Int64,Admin.Application.MqttBrokerServices.Dto.PutAclRuleInput)">
            <summary>
            编辑
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttAclManagementService.DeleteAsync(System.Int64)">
            <summary>
            删除
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttAclManagementService.BatchDeleteAsync(System.Collections.Generic.List{System.Int64})">
            <summary>
            批量删除
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttAclManagementService.TestPermissionAsync(Admin.Application.MqttBrokerServices.Dto.TestPermissionInput)">
            <summary>
            测试权限
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttAclManagementService.ToggleAsync(System.Int64,System.Boolean)">
            <summary>
            启用/禁用ACL规则
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttAclManagementService.ImportAsync(System.Collections.Generic.List{Admin.Application.MqttBrokerServices.Dto.AddAclRuleInput})">
            <summary>
            导入ACL规则
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttAclManagementService.ExportAsync">
            <summary>
            导出ACL规则
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttAclManagementService.GetActiveRulesAsync">
            <summary>
            获取激活的规则列表
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttAclManagementService.UpdateMqttBrokerAclConfigAsync">
            <summary>
            更新MQTT代理的ACL配置
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttAclManagementService.FindMatchingRules(System.String,System.String,System.String,Admin.Communication.Mqtt.Configuration.MqttAccessType,System.Collections.Generic.List{Admin.SqlSugar.Entity.Business.LOT.MqttAclRuleEntity})">
            <summary>
            查找匹配的规则
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttAclManagementService.IsTopicMatch(System.String,System.String)">
            <summary>
            检查主题是否匹配ACL规则（支持MQTT通配符）
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttAclManagementService.IsTopicPatternMatch(System.String[],System.String[],System.Int32,System.Int32)">
            <summary>
            递归检查主题模式匹配
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttAclManagementService.IsValidTopicPattern(System.String)">
            <summary>
            验证主题格式是否有效
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttAclManagementService.CreateDeviceAclRulesAsync(Admin.Application.MqttBrokerServices.Dto.CreateDeviceAclRulesInput)">
            <summary>
            为设备创建ACL规则
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttAclManagementService.DeleteDeviceAclRulesAsync(Admin.Application.MqttBrokerServices.Dto.DeleteDeviceAclRulesInput)">
            <summary>
            删除设备的ACL规则
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttAclManagementService.QueryDeviceAclRulesAsync(Admin.Application.MqttBrokerServices.Dto.QueryDeviceAclRulesInput)">
            <summary>
            查询设备的ACL规则
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttAclManagementService.UpdateDeviceAclRulesAsync(System.String,Admin.Application.MqttBrokerServices.Dto.CreateDeviceAclRulesInput)">
            <summary>
            更新设备ACL规则（当设备信息变更时）
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttAclManagementService.GetDeviceAclRuleTemplatesAsync(Admin.Application.MqttBrokerServices.Dto.CreateDeviceAclRulesInput)">
             <summary>
             获取设备ACL规则模板（系统预置模板）
             </summary>
            
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttAclManagementService.ReplacePlaceholders(System.String,Admin.Application.MqttBrokerServices.Dto.CreateDeviceAclRulesInput)">
            <summary>
            替换模板中的占位符
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.IMqttBrokerManagementService">
            <summary>
            MQTT代理服务管理应用层服务接口
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.IMqttBrokerManagementService.StartAsync(Admin.Application.MqttBrokerServices.Dto.StartBrokerInput,System.Threading.CancellationToken)">
            <summary>
            启动代理服务
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.IMqttBrokerManagementService.StopAsync(System.Threading.CancellationToken)">
            <summary>
            停止代理服务
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.IMqttBrokerManagementService.RestartAsync(Admin.Application.MqttBrokerServices.Dto.RestartBrokerInput,System.Threading.CancellationToken)">
            <summary>
            重启代理服务
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.IMqttBrokerManagementService.GetStatusAsync">
            <summary>
            获取代理服务状态
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.IMqttBrokerManagementService.GetConfigurationAsync">
            <summary>
            获取代理服务配置
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.IMqttBrokerManagementService.UpdateConfigurationAsync(Admin.Application.MqttBrokerServices.Dto.UpdateConfigurationRequest)">
            <summary>
            更新代理服务配置
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.MqttBrokerManagementService">
            <summary>
            MQTT代理服务管理应用层服务
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttBrokerManagementService.#ctor(Admin.Communication.Mqtt.Abstractions.IMqttBroker,Microsoft.Extensions.Logging.ILogger{Admin.Application.MqttBrokerServices.MqttBrokerManagementService},Microsoft.Extensions.Options.IOptionsMonitor{Admin.Communication.Mqtt.Configuration.MqttBrokerOptions})">
            <summary>
            MQTT代理服务管理应用层服务
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttBrokerManagementService.StartAsync(Admin.Application.MqttBrokerServices.Dto.StartBrokerInput,System.Threading.CancellationToken)">
            <summary>
            启动代理服务
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttBrokerManagementService.StopAsync(System.Threading.CancellationToken)">
            <summary>
            停止代理服务
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttBrokerManagementService.RestartAsync(Admin.Application.MqttBrokerServices.Dto.RestartBrokerInput,System.Threading.CancellationToken)">
            <summary>
            重启代理服务
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttBrokerManagementService.GetStatusAsync">
            <summary>
            获取代理服务状态
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttBrokerManagementService.GetConfigurationAsync">
            <summary>
            获取代理服务配置
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttBrokerManagementService.UpdateConfigurationAsync(Admin.Application.MqttBrokerServices.Dto.UpdateConfigurationRequest)">
            <summary>
            更新代理服务配置
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttBrokerManagementService.ApplyConfigurationUpdates(Admin.Application.MqttBrokerServices.Dto.BrokerStartConfigurationInput)">
            <summary>
            应用配置更新
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttBrokerManagementService.ValidateConfiguration(Admin.Application.MqttBrokerServices.Dto.UpdateConfigurationRequest)">
            <summary>
            验证配置
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.IMqttConnectionManagementService">
            <summary>
            MQTT连接管理服务接口
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.IMqttConnectionManagementService.GetConnectionsAsync(Admin.Application.MqttBrokerServices.Dto.GetConnectionsRequest)">
            <summary>
            获取所有连接
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.IMqttConnectionManagementService.GetConnectionDetailAsync(System.String)">
            <summary>
            获取单个连接详情
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.IMqttConnectionManagementService.DisconnectConnectionAsync(System.String,Admin.Application.MqttBrokerServices.Dto.DisconnectConnectionRequest)">
            <summary>
            断开指定连接
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.IMqttConnectionManagementService.BatchDisconnectAsync(Admin.Application.MqttBrokerServices.Dto.BatchDisconnectRequest)">
            <summary>
            批量断开连接
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.MqttConnectionManagementService">
            <summary>
            MQTT连接管理服务
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttConnectionManagementService.#ctor(Admin.Communication.Mqtt.Abstractions.IMqttBroker,Admin.Communication.Mqtt.Abstractions.IMqttConnectionManager,Microsoft.Extensions.Logging.ILogger{Admin.Application.MqttBrokerServices.MqttConnectionManagementService})">
            <summary>
            MQTT连接管理服务
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttConnectionManagementService.GetConnectionsAsync(Admin.Application.MqttBrokerServices.Dto.GetConnectionsRequest)">
            <summary>
            获取所有连接
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttConnectionManagementService.GetConnectionDetailAsync(System.String)">
            <summary>
            获取单个连接详情
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttConnectionManagementService.DisconnectConnectionAsync(System.String,Admin.Application.MqttBrokerServices.Dto.DisconnectConnectionRequest)">
            <summary>
            断开指定连接
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttConnectionManagementService.BatchDisconnectAsync(Admin.Application.MqttBrokerServices.Dto.BatchDisconnectRequest)">
            <summary>
            批量断开连接
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttConnectionManagementService.MapToConnectionInfo(Admin.Communication.Mqtt.Models.MqttClientInfo)">
            <summary>
            将MqttClientInfo映射为ConnectionInfoDto
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttConnectionManagementService.MapToConnectionDetail(Admin.Communication.Mqtt.Models.MqttClientInfo,Admin.Communication.Mqtt.Models.MqttClientConnection)">
            <summary>
            将客户端信息映射为连接详情
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttConnectionManagementService.FormatDuration(System.TimeSpan)">
            <summary>
            格式化持续时间
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.IMqttMessagePublishService">
            <summary>
            MQTT消息发布管理服务接口
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.IMqttMessagePublishService.PublishMessageAsync(Admin.Application.MqttBrokerServices.Dto.PublishMessageRequest)">
            <summary>
            发布消息
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.IMqttMessagePublishService.BatchPublishAsync(Admin.Application.MqttBrokerServices.Dto.BatchPublishRequest)">
            <summary>
            批量发布消息
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.IMqttMessagePublishService.GetRetainedMessagesAsync(Admin.Application.MqttBrokerServices.Dto.GetRetainedMessagesInput)">
            <summary>
            获取保留消息列表
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.IMqttMessagePublishService.ClearRetainedMessageAsync(Admin.Application.MqttBrokerServices.Dto.ClearRetainedMessageInput)">
            <summary>
            清除指定主题的保留消息
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.MqttMessagePublishService">
            <summary>
            MQTT消息发布管理服务
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttMessagePublishService.#ctor(Admin.Communication.Mqtt.Abstractions.IMqttBroker,Admin.Communication.Mqtt.Abstractions.IMqttConnectionManager,SqlSugar.ISqlSugarClient,Microsoft.Extensions.Logging.ILogger{Admin.Application.MqttBrokerServices.MqttMessagePublishService})">
            <summary>
            MQTT消息发布管理服务
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttMessagePublishService.PublishMessageAsync(Admin.Application.MqttBrokerServices.Dto.PublishMessageRequest)">
            <summary>
            发布消息
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttMessagePublishService.BatchPublishAsync(Admin.Application.MqttBrokerServices.Dto.BatchPublishRequest)">
            <summary>
            批量发布消息
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttMessagePublishService.GetRetainedMessagesAsync(Admin.Application.MqttBrokerServices.Dto.GetRetainedMessagesInput)">
            <summary>
            获取保留消息列表
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttMessagePublishService.ClearRetainedMessageAsync(Admin.Application.MqttBrokerServices.Dto.ClearRetainedMessageInput)">
            <summary>
            清除指定主题的保留消息
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttMessagePublishService.GetTopicSubscriberCount(System.String)">
            <summary>
            获取主题的订阅者数量
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttMessagePublishService.ValidateTopic(System.String)">
            <summary>
            验证主题格式
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttMessagePublishService.ValidatePayloadSize(System.String)">
            <summary>
            验证负载大小
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttMessagePublishService.UpdateRetainedMessageAsync(System.String,System.String,System.Int32,Admin.Application.MqttBrokerServices.Dto.PayloadEncoding,System.String,System.Int32)">
            <summary>
            更新保留消息到数据库
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.IMqttSessionManagementService">
            <summary>
            MQTT会话管理服务接口
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.IMqttSessionManagementService.GetSessionsAsync(Admin.Application.MqttBrokerServices.Dto.GetSessionsRequest)">
            <summary>
            获取所有会话
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.IMqttSessionManagementService.GetSessionDetailAsync(System.String)">
            <summary>
            获取单个会话详情
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.IMqttSessionManagementService.GetSessionByClientIdAsync(System.String)">
            <summary>
            根据客户端ID获取会话详情
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.IMqttSessionManagementService.UpdateSessionStateAsync(System.String,Admin.Application.MqttBrokerServices.Dto.UpdateSessionStateRequest)">
            <summary>
            更新会话状态
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.IMqttSessionManagementService.CleanupSessionAsync(System.String)">
            <summary>
            清理会话
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.IMqttSessionManagementService.BatchSessionOperationAsync(Admin.Application.MqttBrokerServices.Dto.BatchSessionOperationRequest)">
            <summary>
            批量会话操作
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.IMqttSessionManagementService.CleanupExpiredSessionsAsync">
            <summary>
            清理过期会话
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.MqttSessionManagementService">
            <summary>
            MQTT会话管理服务实现
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttSessionManagementService.#ctor(Admin.Communication.Mqtt.Abstractions.IMqttBroker,Admin.Communication.Mqtt.Abstractions.IMqttConnectionManager,Microsoft.Extensions.Logging.ILogger{Admin.Application.MqttBrokerServices.MqttSessionManagementService})">
            <summary>
            MQTT会话管理服务实现
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttSessionManagementService.GetSessionsAsync(Admin.Application.MqttBrokerServices.Dto.GetSessionsRequest)">
            <summary>
            获取所有会话
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttSessionManagementService.GetSessionDetailAsync(System.String)">
            <summary>
            获取单个会话详情
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttSessionManagementService.GetSessionByClientIdAsync(System.String)">
            <summary>
            根据客户端ID获取会话详情
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttSessionManagementService.UpdateSessionStateAsync(System.String,Admin.Application.MqttBrokerServices.Dto.UpdateSessionStateRequest)">
            <summary>
            更新会话状态
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttSessionManagementService.CleanupSessionAsync(System.String)">
            <summary>
            清理会话
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttSessionManagementService.BatchSessionOperationAsync(Admin.Application.MqttBrokerServices.Dto.BatchSessionOperationRequest)">
            <summary>
            批量会话操作
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttSessionManagementService.CleanupExpiredSessionsAsync">
            <summary>
            清理过期会话
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttSessionManagementService.GetAllSessionsAsync">
            <summary>
            获取所有会话
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttSessionManagementService.FindSessionByIdAsync(System.String)">
            <summary>
            根据会话ID查找会话
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttSessionManagementService.FindSessionByClientIdAsync(System.String)">
            <summary>
            根据客户端ID查找会话
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttSessionManagementService.GetAwaitingAckMessageCount(Admin.Communication.Mqtt.Models.MqttSession)">
            <summary>
            获取待确认消息数量（通过反射或其他方式）
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttSessionManagementService.GetAwaitingCompMessageCount(Admin.Communication.Mqtt.Models.MqttSession)">
            <summary>
            获取待完成消息数量（通过反射或其他方式）
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttSessionManagementService.MapToSessionInfo(Admin.Communication.Mqtt.Models.MqttSession)">
            <summary>
            映射会话信息到DTO
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttSessionManagementService.MapToSessionDetail(Admin.Communication.Mqtt.Models.MqttSession)">
            <summary>
            映射会话详情到DTO
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttSessionManagementService.MapSubscriptions(Admin.Communication.Mqtt.Models.MqttSession)">
            <summary>
            映射订阅信息
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttSessionManagementService.MapPendingMessages(Admin.Communication.Mqtt.Models.MqttSession)">
            <summary>
            映射待发送消息
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttSessionManagementService.MapStatistics(Admin.Communication.Mqtt.Models.MqttSession)">
            <summary>
            映射统计信息
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttSessionManagementService.FormatDuration(System.TimeSpan)">
            <summary>
            格式化持续时间
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.IMqttStatisticsService">
            <summary>
            MQTT统计监控服务接口
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.IMqttStatisticsService.GetOverviewAsync">
            <summary>
            获取总体统计概览
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.IMqttStatisticsService.GetPerformanceMetricsAsync(Admin.Application.MqttBrokerServices.Dto.GetPerformanceMetricsRequest)">
            <summary>
            获取实时性能指标
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.IMqttStatisticsService.GetConnectionStatisticsAsync(Admin.Application.MqttBrokerServices.Dto.GetConnectionStatisticsRequest)">
            <summary>
            获取连接统计
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.IMqttStatisticsService.GetMessageStatisticsAsync(Admin.Application.MqttBrokerServices.Dto.GetMessageStatisticsRequest)">
            <summary>
            获取消息统计
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.IMqttStatisticsService.GetTopicAnalysisAsync(Admin.Application.MqttBrokerServices.Dto.GetTopicAnalysisRequest)">
            <summary>
            获取主题分析
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.IMqttStatisticsService.GetSessionStatisticsAsync(Admin.Application.MqttBrokerServices.Dto.GetSessionStatisticsRequest)">
            <summary>
            获取会话统计
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.IMqttStatisticsService.GetHistoricalStatisticsAsync(Admin.Application.MqttBrokerServices.Dto.GetHistoricalStatisticsRequest)">
            <summary>
            获取历史统计数据
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.IMqttStatisticsService.ExportStatisticsReportAsync(Admin.Application.MqttBrokerServices.Dto.ExportStatisticsRequest)">
            <summary>
            导出统计报告
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.MqttStatisticsService">
            <summary>
            MQTT统计监控服务实现
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttStatisticsService.#ctor(Admin.Communication.Mqtt.Abstractions.IMqttBroker,Admin.Communication.Mqtt.Abstractions.IMqttConnectionManager,Microsoft.Extensions.Logging.ILogger{Admin.Application.MqttBrokerServices.MqttStatisticsService})">
            <summary>
            MQTT统计监控服务实现
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttStatisticsService.EnsureInitialized">
            <summary>
            确保事件订阅已初始化
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttStatisticsService.GetOverviewAsync">
            <summary>
            获取总体统计概览
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttStatisticsService.GetPerformanceMetricsAsync(Admin.Application.MqttBrokerServices.Dto.GetPerformanceMetricsRequest)">
            <summary>
            获取实时性能指标
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttStatisticsService.GetConnectionStatisticsAsync(Admin.Application.MqttBrokerServices.Dto.GetConnectionStatisticsRequest)">
            <summary>
            获取连接统计
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttStatisticsService.GetMessageStatisticsAsync(Admin.Application.MqttBrokerServices.Dto.GetMessageStatisticsRequest)">
            <summary>
            获取消息统计
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttStatisticsService.GetTopicAnalysisAsync(Admin.Application.MqttBrokerServices.Dto.GetTopicAnalysisRequest)">
            <summary>
            获取主题分析
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttStatisticsService.GetSessionStatisticsAsync(Admin.Application.MqttBrokerServices.Dto.GetSessionStatisticsRequest)">
            <summary>
            获取会话统计
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttStatisticsService.GetHistoricalStatisticsAsync(Admin.Application.MqttBrokerServices.Dto.GetHistoricalStatisticsRequest)">
            <summary>
            获取历史统计数据
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttStatisticsService.ExportStatisticsReportAsync(Admin.Application.MqttBrokerServices.Dto.ExportStatisticsRequest)">
            <summary>
            导出统计报告
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttStatisticsService.SubscribeToMqttEvents">
            <summary>
            订阅MQTT事件以收集统计信息
            </summary>
        </member>
        <member name="T:Admin.Application.MqttBrokerServices.MqttUserManagementService">
            <summary>
            MQTT用户管理应用服务
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttUserManagementService.#ctor(Admin.Communication.Mqtt.Abstractions.IMqttUserService,Microsoft.Extensions.Logging.ILogger{Admin.Application.MqttBrokerServices.MqttUserManagementService})">
            <summary>
            MQTT用户管理应用服务
            </summary>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttUserManagementService.AddAsync(Admin.Application.MqttBrokerServices.Dto.AddMqttUserInput)">
            <summary>
            添加MQTT用户
            </summary>
            <param name="input">用户信息</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttUserManagementService.UpdateAsync(System.String,Admin.Application.MqttBrokerServices.Dto.UpdateMqttUserInput)">
            <summary>
            更新MQTT用户
            </summary>
            <param name="username">用户名</param>
            <param name="input">更新信息</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttUserManagementService.DeleteAsync(System.String)">
            <summary>
            删除MQTT用户
            </summary>
            <param name="username">用户名</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttUserManagementService.QueryAsync(Admin.Application.MqttBrokerServices.Dto.MqttUserQueryInput)">
            <summary>
            分页查询MQTT用户
            </summary>
            <param name="input">查询条件</param>
            <returns>分页结果</returns>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttUserManagementService.GetAsync(System.String)">
            <summary>
            获取MQTT用户详情
            </summary>
            <param name="username">用户名</param>
            <returns>用户信息</returns>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttUserManagementService.ToggleAsync(System.String,System.Boolean)">
            <summary>
            启用/禁用MQTT用户
            </summary>
            <param name="username">用户名</param>
            <param name="isEnabled">是否启用</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttUserManagementService.ResetPasswordAsync(System.String,System.String)">
            <summary>
            重置MQTT用户密码
            </summary>
            <param name="username">用户名</param>
            <param name="newPassword">新密码</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttUserManagementService.CreateDeviceUserAsync(Admin.Application.MqttBrokerServices.Dto.CreateDeviceMqttUserInput)">
            <summary>
            为设备创建MQTT用户
            </summary>
            <param name="input">设备信息</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:Admin.Application.MqttBrokerServices.MqttUserManagementService.DeleteDeviceUserAsync(System.String)">
            <summary>
            删除设备MQTT用户
            </summary>
            <param name="deviceId">设备ID</param>
            <returns>是否成功</returns>
        </member>
        <member name="P:Admin.Application.NoticeServices.Dtos.AddNoticeInput.Title">
            <summary>
            标题
            </summary>
        </member>
        <member name="P:Admin.Application.NoticeServices.Dtos.AddNoticeInput.Content">
            <summary>
            内容
            </summary>
        </member>
        <member name="P:Admin.Application.NoticeServices.Dtos.AddNoticeInput.NoticeType">
            <summary>
            类型
            </summary>
        </member>
        <member name="P:Admin.Application.NoticeServices.Dtos.AddNoticeInput.Level">
            <summary>
            级别
            </summary>
        </member>
        <member name="P:Admin.Application.NoticeServices.Dtos.AddNoticeInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.Application.NoticeServices.Dtos.GetPagedListInput.Title">
            <summary>
            标题
            </summary>
        </member>
        <member name="P:Admin.Application.NoticeServices.Dtos.GetPagedListInput.NoticeType">
            <summary>
            类型
            </summary>
        </member>
        <member name="P:Admin.Application.NoticeServices.Dtos.GetPagedListInput.Level">
            <summary>
            级别
            </summary>
        </member>
        <member name="P:Admin.Application.NoticeServices.Dtos.NoticeOutput.Id">
            <summary>
            Id
            </summary>
        </member>
        <member name="P:Admin.Application.NoticeServices.Dtos.NoticeOutput.Title">
            <summary>
            标题
            </summary>
        </member>
        <member name="P:Admin.Application.NoticeServices.Dtos.NoticeOutput.Content">
            <summary>
            内容
            </summary>
        </member>
        <member name="P:Admin.Application.NoticeServices.Dtos.NoticeOutput.NoticeType">
            <summary>
            类型
            </summary>
        </member>
        <member name="P:Admin.Application.NoticeServices.Dtos.NoticeOutput.Level">
            <summary>
            级别
            </summary>
        </member>
        <member name="P:Admin.Application.NoticeServices.Dtos.NoticeOutput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.Application.NoticeServices.Dtos.NoticeOutput.NoticeTypeString">
            <summary>
            类型string
            </summary>
        </member>
        <member name="P:Admin.Application.NoticeServices.Dtos.NoticeOutput.LevelString">
            <summary>
            级别string
            </summary>
        </member>
        <member name="T:Admin.Application.NoticeServices.NoticeService">
            <summary>
            通知公告
            </summary>
            <param name="db"></param>
            <param name="backgroundJobManager"></param>
        </member>
        <member name="M:Admin.Application.NoticeServices.NoticeService.#ctor(SqlSugar.ISqlSugarClient,Volo.Abp.BackgroundJobs.IBackgroundJobManager)">
            <summary>
            通知公告
            </summary>
            <param name="db"></param>
            <param name="backgroundJobManager"></param>
        </member>
        <member name="F:Admin.Application.NoticeServices.NoticeService._db">
            <summary>
            db
            </summary>
        </member>
        <member name="F:Admin.Application.NoticeServices.NoticeService._backgroundJobManager">
            <summary>
            IBackgroundJobManager
            </summary>
        </member>
        <member name="M:Admin.Application.NoticeServices.NoticeService.GetPagedListAsync(Admin.Application.NoticeServices.Dtos.GetPagedListInput)">
            <summary>
            分页查询
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.NoticeServices.NoticeService.GetAsync(System.Int64)">
            <summary>
            单条查询
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.NoticeServices.NoticeService.AddAsync(Admin.Application.NoticeServices.Dtos.AddNoticeInput)">
            <summary>
            添加
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.NoticeServices.NoticeService.PutAsync(System.Int64,Admin.Application.NoticeServices.Dtos.PutNoticeInput)">
            <summary>
            编辑
            </summary>
            <param name="id"></param>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.NoticeServices.NoticeService.DeleteAsync(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.NoticeServices.NoticeService.SendAsync(System.Int64,System.Int64[])">
            <summary>
            发送通知
            </summary>
            <param name="userIds"></param>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="T:Admin.Application.OrganizationServices.Dtos.AddOrganizationInput">
            <summary>
            组织机构添加
            </summary>
        </member>
        <member name="P:Admin.Application.OrganizationServices.Dtos.AddOrganizationInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.Application.OrganizationServices.Dtos.AddOrganizationInput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:Admin.Application.OrganizationServices.Dtos.AddOrganizationInput.ParentId">
            <summary>
            父级Id
            </summary>
        </member>
        <member name="P:Admin.Application.OrganizationServices.Dtos.AddOrganizationInput.Telephone">
            <summary>
            联系电话
            </summary>
        </member>
        <member name="P:Admin.Application.OrganizationServices.Dtos.AddOrganizationInput.Leader">
            <summary>
            负责人
            </summary>
        </member>
        <member name="P:Admin.Application.OrganizationServices.Dtos.AddOrganizationInput.Sort">
            <summary>
            排序
            </summary>
        </member>
        <member name="T:Admin.Application.OrganizationServices.Dtos.GetPagedListInput">
            <summary>
            组织机构查询
            </summary>
        </member>
        <member name="P:Admin.Application.OrganizationServices.Dtos.GetPagedListInput.Name">
            <summary>
            组织机构名称
            </summary>
        </member>
        <member name="T:Admin.Application.OrganizationServices.Dtos.OrganizationOutput">
            <summary>
            组织机构详情
            </summary>
        </member>
        <member name="P:Admin.Application.OrganizationServices.Dtos.OrganizationOutput.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:Admin.Application.OrganizationServices.Dtos.OrganizationOutput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.Application.OrganizationServices.Dtos.OrganizationOutput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:Admin.Application.OrganizationServices.Dtos.OrganizationOutput.ParentId">
            <summary>
            父级Id
            </summary>
        </member>
        <member name="P:Admin.Application.OrganizationServices.Dtos.OrganizationOutput.Telephone">
            <summary>
            联系电话
            </summary>
        </member>
        <member name="P:Admin.Application.OrganizationServices.Dtos.OrganizationOutput.Leader">
            <summary>
            负责人
            </summary>
        </member>
        <member name="P:Admin.Application.OrganizationServices.Dtos.OrganizationOutput.Sort">
            <summary>
            排序
            </summary>
        </member>
        <member name="P:Admin.Application.OrganizationServices.Dtos.OrganizationOutput.Children">
            <summary>
            子集
            </summary>
        </member>
        <member name="T:Admin.Application.OrganizationServices.OrganizationService">
            <summary>
            组织机构服务
            </summary>
        </member>
        <member name="M:Admin.Application.OrganizationServices.OrganizationService.#ctor(Admin.SqlSugar.Repository{Admin.SqlSugar.Entity.OrganizationEntity},Admin.Multiplex.Contracts.IAdminUser.ICurrentUser)">
            <summary>
            组织机构服务
            </summary>
        </member>
        <member name="M:Admin.Application.OrganizationServices.OrganizationService.GetPagedListAsync(Admin.Application.OrganizationServices.Dtos.GetPagedListInput)">
            <summary>
            分页查询
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.OrganizationServices.OrganizationService.GetAsync(System.Int64)">
            <summary>
            单条查询
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.OrganizationServices.OrganizationService.AddAsync(Admin.Application.OrganizationServices.Dtos.AddOrganizationInput)">
            <summary>
            添加
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.OrganizationServices.OrganizationService.PutAsync(System.Int64,Admin.Application.OrganizationServices.Dtos.AddOrganizationInput)">
            <summary>
            编辑
            </summary>
            <param name="id"></param>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.OrganizationServices.OrganizationService.DeleteAsync(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="T:Admin.Application.ProductServices.Dto.ModelInstructionInput">
            <summary>
            模型指令输入DTO
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelInstructionInput.ModelId">
            <summary>
            模型ID
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelInstructionInput.InstructionName">
            <summary>
            指令名称
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelInstructionInput.FunctionCode">
            <summary>
            功能码
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelInstructionInput.StartAddress">
            <summary>
            起始地址
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelInstructionInput.ReadCount">
            <summary>
            读取数量
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelInstructionInput.Encode">
            <summary>
            编码 (1:HEX 2:ASCII)
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelInstructionInput.ResponseTime">
            <summary>
            响应时间(毫秒)
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelInstructionInput.RetryCount">
            <summary>
            重试次数
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelInstructionInput.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="T:Admin.Application.ProductServices.Dto.UpdateModelInstructionInput">
            <summary>
            模型指令更新输入DTO
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.UpdateModelInstructionInput.Id">
            <summary>
            指令ID
            </summary>
        </member>
        <member name="T:Admin.Application.ProductServices.Dto.ModelInstructionOutput">
            <summary>
            模型指令输出DTO
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelInstructionOutput.Id">
            <summary>
            指令ID
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelInstructionOutput.ModelId">
            <summary>
            模型ID
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelInstructionOutput.ModelName">
            <summary>
            模型名称
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelInstructionOutput.InstructionName">
            <summary>
            指令名称
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelInstructionOutput.FunctionCode">
            <summary>
            功能码
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelInstructionOutput.FunctionCodeName">
            <summary>
            功能码名称
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelInstructionOutput.StartAddress">
            <summary>
            起始地址
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelInstructionOutput.ReadCount">
            <summary>
            读取数量
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelInstructionOutput.Encode">
            <summary>
            编码
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelInstructionOutput.EncodeName">
            <summary>
            编码名称
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelInstructionOutput.ResponseTime">
            <summary>
            响应时间(毫秒)
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelInstructionOutput.RetryCount">
            <summary>
            重试次数
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelInstructionOutput.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelInstructionOutput.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelInstructionOutput.UpdateTime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelInstructionOutput.CreateBy">
            <summary>
            创建人
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelInstructionOutput.UpdateBy">
            <summary>
            更新人
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelInstructionOutput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Admin.Application.ProductServices.Dto.ModelInstructionQueryInput">
            <summary>
            模型指令查询输入DTO
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelInstructionQueryInput.ModelId">
            <summary>
            模型ID
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelInstructionQueryInput.InstructionName">
            <summary>
            指令名称
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelInstructionQueryInput.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="T:Admin.Application.ProductServices.Dto.ModelInstructionSimpleOutput">
            <summary>
            模型指令简单输出DTO (用于下拉选择等场景)
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelInstructionSimpleOutput.Id">
            <summary>
            指令ID
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelInstructionSimpleOutput.ModelId">
            <summary>
            模型ID
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelInstructionSimpleOutput.InstructionName">
            <summary>
            指令名称
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelInstructionSimpleOutput.FunctionCode">
            <summary>
            功能码
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelInstructionSimpleOutput.FunctionCodeName">
            <summary>
            功能码名称
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelInstructionSimpleOutput.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="T:Admin.Application.ProductServices.Dto.ModelPropertyInput">
            <summary>
            模型属性输入DTO
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyInput.ModelId">
            <summary>
            模型ID
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyInput.InstructionId">
            <summary>
            指令ID
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyInput.Name">
            <summary>
            参数名称
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyInput.Sort">
            <summary>
            参数序号
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyInput.Key">
            <summary>
            参数标识符 (JSON中的路径，支持多层嵌套，如: "IO.input.channel1" 或 "sensors.temperature")
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyInput.DataType">
            <summary>
            数据类型（1:decimal 模拟量 2:string字符串 3:datetime时间 4:json 5:enum枚举）
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyInput.EnumDescription">
            <summary>
            枚举说明
            示例: 0=运行;1=停止
            0=浮冲;1=均冲;2=放电
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyInput.Unit">
            <summary>
            单位
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyInput.DivisionFactor">
            <summary>
            倍率
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyInput.DecimalPlaces">
            <summary>
            小数位数
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyInput.CorrectionScale">
            <summary>
            校正比例
            解析值*校正比例+校正幅度，默认为1
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyInput.CorrectionAmplitude">
            <summary>
            校正幅度
            默认为0
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyInput.AlarmUpperLimit">
            <summary>
            报警上限
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyInput.AlarmUpperLimitClearValue">
            <summary>
            报警上限解除值
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyInput.AlarmLowerLimit">
            <summary>
            报警下限
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyInput.AlarmLowerLimitClearValue">
            <summary>
            报警下限解除值
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyInput.WarningUpperLimit">
            <summary>
            预警上限
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyInput.WarningUpperLimitClearValue">
            <summary>
            预警上限解除值
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyInput.WarningLowerLimit">
            <summary>
            预警下限
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyInput.WarningLowerLimitClearValue">
            <summary>
            预警下限解除值
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyInput.IsSave">
            <summary>
            是否保存
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyInput.SaveAmplitude">
            <summary>
            保存幅度
            与上次保存值的偏差超过幅度，触发保存
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyInput.SaveAmplitudeType">
            <summary>
            保存幅度类型
            0: 数值 1：百分比
            默认数值
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyInput.SaveInterval">
            <summary>
            保存间隔(毫秒)
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyInput.MonitorStatus">
            <summary>
            监控状态
            0: 不启用 1：启用
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyInput.Description">
            <summary>
            参数描述
            </summary>
        </member>
        <member name="T:Admin.Application.ProductServices.Dto.UpdateModelPropertyInput">
            <summary>
            模型属性更新输入DTO
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.UpdateModelPropertyInput.Id">
            <summary>
            属性ID
            </summary>
        </member>
        <member name="T:Admin.Application.ProductServices.Dto.ModelPropertyOutput">
            <summary>
            模型属性输出DTO
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyOutput.Id">
            <summary>
            属性ID
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyOutput.ModelId">
            <summary>
            模型ID
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyOutput.InstructionId">
            <summary>
            指令ID
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyOutput.Name">
            <summary>
            参数名称
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyOutput.Sort">
            <summary>
            参数序号
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyOutput.Key">
            <summary>
            参数标识符 (JSON中的路径，支持多层嵌套，如: "IO.input.channel1" 或 "sensors.temperature")
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyOutput.DataType">
            <summary>
            数据类型（1:decimal 模拟量 2:string字符串 3:datetime时间 4:json 5:enum枚举）
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyOutput.DataTypeName">
            <summary>
            数据类型名称
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyOutput.EnumDescription">
            <summary>
            枚举说明
            示例: 0=运行;1=停止
            0=浮冲;1=均冲;2=放电
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyOutput.Unit">
            <summary>
            单位
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyOutput.DivisionFactor">
            <summary>
            倍率
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyOutput.DecimalPlaces">
            <summary>
            小数位数
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyOutput.CorrectionScale">
            <summary>
            校正比例
            解析值*校正比例+校正幅度，默认为1
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyOutput.CorrectionAmplitude">
            <summary>
            校正幅度
            默认为0
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyOutput.AlarmUpperLimit">
            <summary>
            报警上限
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyOutput.AlarmUpperLimitClearValue">
            <summary>
            报警上限解除值
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyOutput.AlarmLowerLimit">
            <summary>
            报警下限
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyOutput.AlarmLowerLimitClearValue">
            <summary>
            报警下限解除值
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyOutput.WarningUpperLimit">
            <summary>
            预警上限
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyOutput.WarningUpperLimitClearValue">
            <summary>
            预警上限解除值
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyOutput.WarningLowerLimit">
            <summary>
            预警下限
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyOutput.WarningLowerLimitClearValue">
            <summary>
            预警下限解除值
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyOutput.IsSave">
            <summary>
            是否保存
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyOutput.SaveAmplitude">
            <summary>
            保存幅度
            与上次保存值的偏差超过幅度，触发保存
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyOutput.SaveAmplitudeType">
            <summary>
            保存幅度类型
            0: 数值 1：百分比
            默认数值
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyOutput.SaveInterval">
            <summary>
            保存间隔(毫秒)
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyOutput.MonitorStatus">
            <summary>
            监控状态
            0: 不启用 1：启用
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyOutput.Description">
            <summary>
            参数描述
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyOutput.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyOutput.UpdateTime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyOutput.CreateBy">
            <summary>
            创建人
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyOutput.UpdateBy">
            <summary>
            更新人
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyOutput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Admin.Application.ProductServices.Dto.ModelPropertyQueryInput">
            <summary>
            模型属性查询输入DTO
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyQueryInput.ModelId">
            <summary>
            模型ID
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyQueryInput.InstructionId">
            <summary>
            指令ID
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyQueryInput.Key">
            <summary>
            参数标识符
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyQueryInput.DataType">
            <summary>
            数据类型
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyQueryInput.MonitorStatus">
            <summary>
            监控状态
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertyQueryInput.IsSave">
            <summary>
            是否保存
            </summary>
        </member>
        <member name="T:Admin.Application.ProductServices.Dto.ModelPropertySimpleOutput">
            <summary>
            模型属性简单输出DTO (用于下拉选择等场景)
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertySimpleOutput.Id">
            <summary>
            属性ID
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertySimpleOutput.Name">
            <summary>
            参数名称
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertySimpleOutput.Key">
            <summary>
            参数标识符
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertySimpleOutput.DataType">
            <summary>
            数据类型
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertySimpleOutput.DataTypeName">
            <summary>
            数据类型名称
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ModelPropertySimpleOutput.Unit">
            <summary>
            单位
            </summary>
        </member>
        <member name="T:Admin.Application.ProductServices.Dto.BatchAddModelPropertyInput">
            <summary>
            批量添加模型属性输入DTO
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.BatchAddModelPropertyInput.ModelId">
            <summary>
            模型ID
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.BatchAddModelPropertyInput.Properties">
            <summary>
            属性列表
            </summary>
        </member>
        <member name="T:Admin.Application.ProductServices.Dto.ProductPropertyInput">
            <summary>
            产品属性输入DTO (向后兼容别名)
            </summary>
        </member>
        <member name="T:Admin.Application.ProductServices.Dto.UpdateProductPropertyInput">
            <summary>
            产品属性更新输入DTO (向后兼容别名)
            </summary>
        </member>
        <member name="T:Admin.Application.ProductServices.Dto.ProductPropertyOutput">
            <summary>
            产品属性输出DTO (向后兼容别名)
            </summary>
        </member>
        <member name="T:Admin.Application.ProductServices.Dto.ProductPropertyQueryInput">
            <summary>
            产品属性查询输入DTO (向后兼容别名)
            </summary>
        </member>
        <member name="T:Admin.Application.ProductServices.Dto.ProductPropertySimpleOutput">
            <summary>
            产品属性简单输出DTO (向后兼容别名)
            </summary>
        </member>
        <member name="T:Admin.Application.ProductServices.Dto.BatchAddProductPropertyInput">
            <summary>
            批量添加产品属性输入DTO (向后兼容别名)
            </summary>
        </member>
        <member name="T:Admin.Application.ProductServices.Dto.ProductInput">
            <summary>
            产品输入DTO
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductInput.ProductName">
            <summary>
            产品名称
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductInput.ProtocolType">
            <summary>
            协议类型 (1:MQTT 2:Modbus)
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductInput.ProductType">
            <summary>
            产品类型 (1:直连设备 2:网关设备 3:网关子设备)
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductInput.DataFormat">
            <summary>
            数据格式 (1:JSON 2:HEX)
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductInput.Description">
            <summary>
            产品描述
            </summary>
        </member>
        <member name="T:Admin.Application.ProductServices.Dto.UpdateProductInput">
            <summary>
            产品更新输入DTO
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.UpdateProductInput.Id">
            <summary>
            产品ID
            </summary>
        </member>
        <member name="T:Admin.Application.ProductServices.Dto.ProductOutput">
            <summary>
            产品输出DTO
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductOutput.Id">
            <summary>
            产品ID
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductOutput.ProductName">
            <summary>
            产品名称
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductOutput.ProtocolType">
            <summary>
            协议类型
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductOutput.ProtocolTypeName">
            <summary>
            协议类型名称
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductOutput.ProductType">
            <summary>
            产品类型
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductOutput.ProductTypeName">
            <summary>
            产品类型名称
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductOutput.DataFormat">
            <summary>
            数据格式
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductOutput.DataFormatName">
            <summary>
            数据格式名称
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductOutput.Description">
            <summary>
            产品描述
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductOutput.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductOutput.UpdateTime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductOutput.CreateBy">
            <summary>
            创建人
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductOutput.UpdateBy">
            <summary>
            更新人
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductOutput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Admin.Application.ProductServices.Dto.ProductQueryInput">
            <summary>
            产品查询输入DTO
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductQueryInput.ProductName">
            <summary>
            产品名称
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductQueryInput.ProtocolType">
            <summary>
            协议类型
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductQueryInput.ProductType">
            <summary>
            产品类型
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductQueryInput.DataFormat">
            <summary>
            数据格式
            </summary>
        </member>
        <member name="T:Admin.Application.ProductServices.Dto.ProductSimpleOutput">
            <summary>
            产品简单输出DTO (用于下拉选择等场景)
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductSimpleOutput.Id">
            <summary>
            产品ID
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductSimpleOutput.ProductName">
            <summary>
            产品名称
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductSimpleOutput.ProtocolType">
            <summary>
            协议类型
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductSimpleOutput.ProductType">
            <summary>
            产品类型
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductSimpleOutput.DataFormat">
            <summary>
            数据格式
            </summary>
        </member>
        <member name="T:Admin.Application.ProductServices.Dto.ProductModelInput">
            <summary>
            产品模型输入DTO
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductModelInput.ProductId">
            <summary>
            产品ID
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductModelInput.ModelName">
            <summary>
            模型名称
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductModelInput.DeviceGroup">
            <summary>
            设备组 (1:温湿度 2:漏水检测 3:空调 4:UPS 5:配电 6:开关 7:发电机 8:红外 9:门禁 10:传感器 11.冷通道)
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductModelInput.Description">
            <summary>
            模型描述
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductModelInput.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="T:Admin.Application.ProductServices.Dto.UpdateProductModelInput">
            <summary>
            产品模型更新输入DTO
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.UpdateProductModelInput.Id">
            <summary>
            模型ID
            </summary>
        </member>
        <member name="T:Admin.Application.ProductServices.Dto.ProductModelOutput">
            <summary>
            产品模型输出DTO
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductModelOutput.Id">
            <summary>
            模型ID
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductModelOutput.ProductId">
            <summary>
            产品ID
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductModelOutput.ProductName">
            <summary>
            产品名称
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductModelOutput.ModelName">
            <summary>
            模型名称
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductModelOutput.DeviceGroup">
            <summary>
            设备组
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductModelOutput.DeviceGroupName">
            <summary>
            设备组名称
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductModelOutput.Description">
            <summary>
            模型描述
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductModelOutput.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="T:Admin.Application.ProductServices.Dto.ProductModelQueryInput">
            <summary>
            产品模型查询输入DTO
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductModelQueryInput.ProductId">
            <summary>
            产品ID
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductModelQueryInput.ModelName">
            <summary>
            模型名称
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductModelQueryInput.DeviceGroup">
            <summary>
            设备组
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductModelQueryInput.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="T:Admin.Application.ProductServices.Dto.ProductModelSimpleOutput">
            <summary>
            产品模型简单输出DTO (用于下拉选择等场景)
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductModelSimpleOutput.Id">
            <summary>
            模型ID
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductModelSimpleOutput.ProductId">
            <summary>
            产品ID
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductModelSimpleOutput.ModelName">
            <summary>
            模型名称
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductModelSimpleOutput.DeviceGroup">
            <summary>
            设备组
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductModelSimpleOutput.DeviceGroupName">
            <summary>
            设备组名称
            </summary>
        </member>
        <member name="T:Admin.Application.ProductServices.Dto.ProductModelDetailOutput">
            <summary>
            产品模型详情输出DTO (包含属性信息)
            </summary>
        </member>
        <member name="P:Admin.Application.ProductServices.Dto.ProductModelDetailOutput.Properties">
            <summary>
            属性列表
            </summary>
        </member>
        <member name="T:Admin.Application.ProductServices.Mapper.ModelInstructionEntityToOutputMapper">
            <summary>
            模型指令实体到输出DTO的映射配置
            </summary>
        </member>
        <member name="M:Admin.Application.ProductServices.Mapper.ModelInstructionEntityToOutputMapper.GetFunctionCodeName(System.Int32)">
            <summary>
            获取功能码名称
            </summary>
        </member>
        <member name="M:Admin.Application.ProductServices.Mapper.ModelInstructionEntityToOutputMapper.GetEncodeName(System.Int32)">
            <summary>
            获取编码名称
            </summary>
        </member>
        <member name="T:Admin.Application.ProductServices.Mapper.ModelPropertyEntityToOutputMapper">
            <summary>
            模型属性实体到输出DTO的映射配置
            </summary>
        </member>
        <member name="M:Admin.Application.ProductServices.Mapper.ModelPropertyEntityToOutputMapper.GetDataTypeName(System.Int32)">
            <summary>
            获取数据类型名称
            </summary>
        </member>
        <member name="T:Admin.Application.ProductServices.Mapper.ProductEntityToOutputMapper">
            <summary>
            产品实体到输出DTO的映射配置
            </summary>
        </member>
        <member name="M:Admin.Application.ProductServices.Mapper.ProductEntityToOutputMapper.GetProtocolTypeName(System.Int32)">
            <summary>
            获取协议类型名称
            </summary>
        </member>
        <member name="M:Admin.Application.ProductServices.Mapper.ProductEntityToOutputMapper.GetProductTypeName(System.Int32)">
            <summary>
            获取产品类型名称
            </summary>
        </member>
        <member name="M:Admin.Application.ProductServices.Mapper.ProductEntityToOutputMapper.GetDataFormatName(System.Int32)">
            <summary>
            获取数据格式名称
            </summary>
        </member>
        <member name="T:Admin.Application.ProductServices.Mapper.ProductModelEntityToOutputMapper">
            <summary>
            产品模型实体到输出DTO的映射配置
            </summary>
        </member>
        <member name="M:Admin.Application.ProductServices.Mapper.ProductModelEntityToOutputMapper.GetDeviceGroupName(System.Int32)">
            <summary>
            获取设备组名称
            </summary>
        </member>
        <member name="T:Admin.Application.ProductServices.ModelInstructionService">
            <summary>
            模型指令服务
            </summary>
        </member>
        <member name="M:Admin.Application.ProductServices.ModelInstructionService.#ctor(SqlSugar.ISqlSugarClient,Admin.SqlSugar.Repository{Admin.SqlSugar.Entity.Business.LOT.ModelInstructionEntity})">
            <summary>
            模型指令服务
            </summary>
        </member>
        <member name="M:Admin.Application.ProductServices.ModelInstructionService.GetPagedListAsync(Admin.Application.ProductServices.Dto.ModelInstructionQueryInput)">
            <summary>
            获取模型指令分页列表
            </summary>
            <param name="input">查询条件</param>
            <returns>分页列表</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ModelInstructionService.GetByIdAsync(System.Int64)">
            <summary>
            根据ID获取模型指令详情
            </summary>
            <param name="id">指令ID</param>
            <returns>指令详情</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ModelInstructionService.CreateAsync(Admin.Application.ProductServices.Dto.ModelInstructionInput)">
            <summary>
            创建模型指令
            </summary>
            <param name="input">创建输入</param>
            <returns>创建结果</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ModelInstructionService.UpdateAsync(Admin.Application.ProductServices.Dto.UpdateModelInstructionInput)">
            <summary>
            更新模型指令
            只能修改InstructionName、FunctionCode、StartAddress、ReadCount、Encode、ResponseTime、RetryCount、IsEnabled
            </summary>
            <param name="input">更新输入</param>
            <returns>更新结果</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ModelInstructionService.DeleteAsync(System.Int64)">
            <summary>
            删除模型指令
            </summary>
            <param name="id">指令ID</param>
            <returns>删除结果</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ModelInstructionService.BatchDeleteAsync(System.Collections.Generic.List{System.Int64})">
            <summary>
            批量删除模型指令
            </summary>
            <param name="ids">指令ID列表</param>
            <returns>删除结果</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ModelInstructionService.GetByModelIdAsync(System.Int64)">
            <summary>
            根据模型ID获取指令列表
            </summary>
            <param name="modelId">模型ID</param>
            <returns>指令列表</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ModelInstructionService.SetEnabledAsync(System.Int64,System.Boolean)">
            <summary>
            设置指令启用状态
            </summary>
            <param name="id">指令ID</param>
            <param name="isEnabled">是否启用</param>
            <returns>设置结果</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ModelInstructionService.GetSimpleListAsync">
            <summary>
            获取所有启用的指令列表
            </summary>
            <returns>指令列表</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ModelInstructionService.GetFunctionCodeName(System.Int32)">
            <summary>
            获取功能码名称
            </summary>
            <param name="functionCode">功能码</param>
            <returns>功能码名称</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ModelInstructionService.GetEncodeName(System.Int32)">
            <summary>
            获取编码名称
            </summary>
            <param name="encode">编码</param>
            <returns>编码名称</returns>
        </member>
        <member name="T:Admin.Application.ProductServices.ModelPropertyService">
            <summary>
            模型属性服务
            </summary>
        </member>
        <member name="M:Admin.Application.ProductServices.ModelPropertyService.#ctor(SqlSugar.ISqlSugarClient,Admin.SqlSugar.Repository{Admin.SqlSugar.Entity.Business.LOT.ModelPropertyEntity})">
            <summary>
            模型属性服务
            </summary>
        </member>
        <member name="M:Admin.Application.ProductServices.ModelPropertyService.GetPagedListAsync(Admin.Application.ProductServices.Dto.ModelPropertyQueryInput)">
            <summary>
            获取模型属性分页列表
            </summary>
            <param name="input">查询条件</param>
            <returns>分页列表</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ModelPropertyService.GetByIdAsync(System.Int64)">
            <summary>
            根据ID获取模型属性详情
            </summary>
            <param name="id">属性ID</param>
            <returns>属性详情</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ModelPropertyService.GetProductPropertyByIdAsync(System.Int64)">
            <summary>
            根据ID获取产品属性详情 (向后兼容)
            </summary>
            <param name="id">属性ID</param>
            <returns>属性详情</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ModelPropertyService.AddAsync(Admin.Application.ProductServices.Dto.ModelPropertyInput)">
            <summary>
            添加模型属性
            </summary>
            <param name="input">属性信息</param>
            <returns>属性ID</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ModelPropertyService.UpdateAsync(Admin.Application.ProductServices.Dto.UpdateModelPropertyInput)">
            <summary>
            更新模型属性
            </summary>
            <param name="input">属性信息</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ModelPropertyService.DeleteAsync(System.Int64)">
            <summary>
            删除模型属性
            </summary>
            <param name="id">属性ID</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ModelPropertyService.BatchDeleteAsync(System.Collections.Generic.List{System.Int64})">
            <summary>
            批量删除模型属性
            </summary>
            <param name="ids">属性ID集合</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ModelPropertyService.GetByModelIdAsync(System.Int64)">
            <summary>
            根据模型ID获取属性列表
            </summary>
            <param name="modelId">模型ID</param>
            <returns>属性列表</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ModelPropertyService.GetProductPropertiesByModelIdAsync(System.Int64)">
            <summary>
            根据模型ID获取属性列表 (向后兼容)
            </summary>
            <param name="modelId">模型ID</param>
            <returns>属性列表</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ModelPropertyService.GetSimpleByModelIdAsync(System.Int64)">
            <summary>
            根据模型ID获取属性简单列表
            </summary>
            <param name="modelId">模型ID</param>
            <returns>属性简单列表</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ModelPropertyService.GetProductPropertySimpleByModelIdAsync(System.Int64)">
            <summary>
            根据模型ID获取属性简单列表 (向后兼容)
            </summary>
            <param name="modelId">模型ID</param>
            <returns>属性简单列表</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ModelPropertyService.BatchAddAsync(Admin.Application.ProductServices.Dto.BatchAddModelPropertyInput)">
            <summary>
            批量添加模型属性
            </summary>
            <param name="input">批量添加信息</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ModelPropertyService.DeleteByModelIdAsync(System.Int64)">
            <summary>
            根据模型ID删除所有属性
            </summary>
            <param name="modelId">模型ID</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ModelPropertyService.GetSimpleListAsync">
            <summary>
            获取属性简单列表 (用于下拉选择)
            </summary>
            <returns>属性简单列表</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ModelPropertyService.GetProductPropertySimpleListAsync">
            <summary>
            获取属性简单列表 (用于下拉选择) (向后兼容)
            </summary>
            <returns>属性简单列表</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ModelPropertyService.GetByInstructionIdAsync(System.Int64)">
            <summary>
            根据指令ID获取属性列表
            </summary>
            <param name="instructionId">指令ID</param>
            <returns>属性列表</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ModelPropertyService.DeleteByInstructionIdAsync(System.Int64)">
            <summary>
            根据指令ID删除所有属性
            </summary>
            <param name="instructionId">指令ID</param>
            <returns>是否成功</returns>
        </member>
        <member name="T:Admin.Application.ProductServices.ProductModelService">
            <summary>
            产品模型服务
            </summary>
        </member>
        <member name="M:Admin.Application.ProductServices.ProductModelService.#ctor(SqlSugar.ISqlSugarClient,Admin.SqlSugar.Repository{Admin.SqlSugar.Entity.Business.LOT.ProductModelEntity})">
            <summary>
            产品模型服务
            </summary>
        </member>
        <member name="M:Admin.Application.ProductServices.ProductModelService.GetPagedListAsync(Admin.Application.ProductServices.Dto.ProductModelQueryInput)">
            <summary>
            获取产品模型分页列表
            </summary>
            <param name="input">查询条件</param>
            <returns>分页列表</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ProductModelService.GetByIdAsync(System.Int64)">
            <summary>
            根据产品ID获取产品模型详情
            </summary>
            <param name="id">模型ID</param>
            <returns>模型详情</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ProductModelService.GetDetailByIdAsync(System.Int64)">
            <summary>
            获取产品模型详情（包含属性信息）
            </summary>
            <param name="id">模型ID</param>
            <returns>模型详情</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ProductModelService.AddAsync(Admin.Application.ProductServices.Dto.ProductModelInput)">
            <summary>
            添加产品模型
            </summary>
            <param name="input">模型信息</param>
            <returns>模型ID</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ProductModelService.UpdateAsync(Admin.Application.ProductServices.Dto.UpdateProductModelInput)">
            <summary>
            更新产品模型
            只能修改ModelName、Description、IsEnabled
            </summary>
            <param name="input">模型信息</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ProductModelService.DeleteAsync(System.Int64)">
            <summary>
            删除产品模型
            </summary>
            <param name="id">模型ID</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ProductModelService.BatchDeleteAsync(System.Collections.Generic.List{System.Int64})">
            <summary>
            批量删除产品模型
            </summary>
            <param name="ids">模型ID集合</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ProductModelService.GetByProductIdAsync(System.Int64)">
            <summary>
            根据产品ID获取模型列表
            </summary>
            <param name="productId">产品ID</param>
            <returns>模型列表</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ProductModelService.GetByDeviceGroupAsync(System.Int32)">
            <summary>
            根据设备组获取模型列表
            </summary>
            <param name="deviceGroup">设备组</param>
            <returns>模型列表</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ProductModelService.SetEnabledAsync(System.Int64,System.Boolean)">
            <summary>
            启用/禁用模型
            </summary>
            <param name="id">模型ID</param>
            <param name="isEnabled">是否启用</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ProductModelService.GetSimpleListAsync">
            <summary>
            获取模型简单列表 (用于下拉选择)
            </summary>
            <returns>模型简单列表</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ProductModelService.GetDeviceGroupName(System.Int32)">
            <summary>
            获取设备组名称
            </summary>
            <param name="deviceGroup">设备组</param>
            <returns>设备组名称</returns>
        </member>
        <member name="T:Admin.Application.ProductServices.ProductService">
            <summary>
            产品服务
            </summary>
        </member>
        <member name="M:Admin.Application.ProductServices.ProductService.#ctor(SqlSugar.ISqlSugarClient,Admin.SqlSugar.Repository{Admin.SqlSugar.Entity.Business.LOT.ProductEntity})">
            <summary>
            产品服务
            </summary>
        </member>
        <member name="M:Admin.Application.ProductServices.ProductService.GetPagedListAsync(Admin.Application.ProductServices.Dto.ProductQueryInput)">
            <summary>
            获取产品分页列表
            </summary>
            <param name="input">查询条件</param>
            <returns>分页列表</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ProductService.GetByIdAsync(System.Int64)">
            <summary>
            根据ID获取产品详情
            </summary>
            <param name="id">产品ID</param>
            <returns>产品详情</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ProductService.AddAsync(Admin.Application.ProductServices.Dto.ProductInput)">
            <summary>
            添加产品
            </summary>
            <param name="input">产品信息</param>
            <returns>产品ID</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ProductService.UpdateAsync(Admin.Application.ProductServices.Dto.UpdateProductInput)">
            <summary>
            更新产品
            允许修改ProductName、Description
            </summary>
            <param name="input">产品信息</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ProductService.DeleteAsync(System.Int64)">
            <summary>
            删除产品
            </summary>
            <param name="id">产品ID</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ProductService.BatchDeleteAsync(System.Collections.Generic.List{System.Int64})">
            <summary>
            批量删除产品
            </summary>
            <param name="ids">产品ID集合</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ProductService.GetSimpleListAsync">
            <summary>
            获取产品简单列表 (用于下拉选择)
            </summary>
            <returns>产品简单列表</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ProductService.GetByProtocolTypeAsync(System.Int32)">
            <summary>
            根据协议类型获取产品列表
            </summary>
            <param name="protocolType">协议类型</param>
            <returns>产品列表</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ProductService.GetByProductTypeAsync(System.Int32)">
            <summary>
            根据产品类型获取产品列表
            </summary>
            <param name="productType">产品类型</param>
            <returns>产品列表</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ProductService.GetProtocolTypeName(System.Int32)">
            <summary>
            获取协议类型名称
            </summary>
            <param name="protocolType">协议类型</param>
            <returns>协议类型名称</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ProductService.GetProductTypeName(System.Int32)">
            <summary>
            获取产品类型名称
            </summary>
            <param name="productType">产品类型</param>
            <returns>产品类型名称</returns>
        </member>
        <member name="M:Admin.Application.ProductServices.ProductService.GetDataFormatName(System.Int32)">
            <summary>
            获取数据格式名称
            </summary>
            <param name="dataFormat">数据格式</param>
            <returns>数据格式名称</returns>
        </member>
        <member name="T:Admin.Application.ProfileSystemServices.Dtos.AddProfileSystemInput">
            <summary>
            系统文件添加
            </summary>
        </member>
        <member name="P:Admin.Application.ProfileSystemServices.Dtos.AddProfileSystemInput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:Admin.Application.ProfileSystemServices.Dtos.AddProfileSystemInput.Code">
            <summary>
            编码
            </summary>
        </member>
        <member name="P:Admin.Application.ProfileSystemServices.Dtos.AddProfileSystemInput.File">
            <summary>
            文件
            </summary>
        </member>
        <member name="P:Admin.Application.ProfileSystemServices.Dtos.AddProfileSystemInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Admin.Application.ProfileSystemServices.Dtos.GetPagedListInput">
            <summary>
            系统配置表查询
            </summary>
        </member>
        <member name="P:Admin.Application.ProfileSystemServices.Dtos.GetPagedListInput.Name">
            <summary>
            配置名称
            </summary>
        </member>
        <member name="P:Admin.Application.ProfileSystemServices.Dtos.GetPagedListInput.Code">
            <summary>
            编码
            </summary>
        </member>
        <member name="T:Admin.Application.ProfileSystemServices.Dtos.ProfileSystemOutput">
            <summary>
            系统文件详情
            </summary>
        </member>
        <member name="P:Admin.Application.ProfileSystemServices.Dtos.ProfileSystemOutput.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:Admin.Application.ProfileSystemServices.Dtos.ProfileSystemOutput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.Application.ProfileSystemServices.Dtos.ProfileSystemOutput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:Admin.Application.ProfileSystemServices.Dtos.ProfileSystemOutput.Code">
            <summary>
            编码
            </summary>
        </member>
        <member name="P:Admin.Application.ProfileSystemServices.Dtos.ProfileSystemOutput.FileId">
            <summary>
            文件Id
            </summary>
        </member>
        <member name="P:Admin.Application.ProfileSystemServices.Dtos.ProfileSystemOutput.FileName">
            <summary>
            文件名称
            </summary>
        </member>
        <member name="P:Admin.Application.ProfileSystemServices.Dtos.ProfileSystemOutput.FileSize">
            <summary>
            文件大小
            </summary>
        </member>
        <member name="T:Admin.Application.ProfileSystemServices.ProfileSystemService">
            <summary>
            系统文件服务
            </summary>
            <param name="db"></param>
            <param name="fileCommand"></param>
            <param name="httpContextAccessor"></param>
            <param name="objectValidator"></param>
        </member>
        <member name="M:Admin.Application.ProfileSystemServices.ProfileSystemService.#ctor(SqlSugar.ISqlSugarClient,Admin.Core.File.IFileCommand{Admin.Core.File.Containers.ProfileSystemContainer},Microsoft.AspNetCore.Http.IHttpContextAccessor,Volo.Abp.Validation.IObjectValidator)">
            <summary>
            系统文件服务
            </summary>
            <param name="db"></param>
            <param name="fileCommand"></param>
            <param name="httpContextAccessor"></param>
            <param name="objectValidator"></param>
        </member>
        <member name="M:Admin.Application.ProfileSystemServices.ProfileSystemService.GetPagedListAsync(Admin.Application.ProfileSystemServices.Dtos.GetPagedListInput)">
            <summary>
            分页查询
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.ProfileSystemServices.ProfileSystemService.GetAsync(System.Int64)">
            <summary>
            单条查询
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.ProfileSystemServices.ProfileSystemService.AddByStreamAsync">
            <summary>
            添加（文件流式上传）
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.ProfileSystemServices.ProfileSystemService.AddAsync(Admin.Application.ProfileSystemServices.Dtos.AddProfileSystemInput)">
            <summary>
            添加
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.ProfileSystemServices.ProfileSystemService.DeleteAsync(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.ProfileSystemServices.ProfileSystemService.DownloadAsync(System.Int64)">
            <summary>
            文件下载
            </summary>
            <param name="fileId"></param>
            <returns>FileStreamResult</returns>
        </member>
        <member name="P:Admin.Application.RequestLogServices.Dtos.GetPagedListInput.RequestDate">
            <summary>
            日期
            </summary>
        </member>
        <member name="P:Admin.Application.RequestLogServices.Dtos.GetPagedListInput.ControllerName">
            <summary>
            控制器名称
            </summary>
        </member>
        <member name="P:Admin.Application.RequestLogServices.Dtos.GetPagedListInput.ActionName">
            <summary>
            方法名
            </summary>
        </member>
        <member name="P:Admin.Application.RequestLogServices.Dtos.GetRequestLogChartInput.StartTime">
            <summary>
            开始时间
            </summary>
        </member>
        <member name="P:Admin.Application.RequestLogServices.Dtos.GetRequestLogChartInput.EndTime">
            <summary>
            结束时间
            </summary>
        </member>
        <member name="P:Admin.Application.RequestLogServices.Dtos.RequestLogOutput.Id">
             <summary>
             Id
            </summary>
        </member>
        <member name="P:Admin.Application.RequestLogServices.Dtos.RequestLogOutput.ControllerName">
            <summary>
            控制器
            </summary>
        </member>
        <member name="P:Admin.Application.RequestLogServices.Dtos.RequestLogOutput.ActionName">
            <summary>
            方法名
            </summary>
        </member>
        <member name="P:Admin.Application.RequestLogServices.Dtos.RequestLogOutput.RequestMethod">
            <summary>
            请求类型
            </summary>
        </member>
        <member name="P:Admin.Application.RequestLogServices.Dtos.RequestLogOutput.EnvironmentName">
            <summary>
            服务器环境
            </summary>
        </member>
        <member name="P:Admin.Application.RequestLogServices.Dtos.RequestLogOutput.IsSuccess">
            <summary>
            完成情况
            </summary>
        </member>
        <member name="P:Admin.Application.RequestLogServices.Dtos.RequestLogOutput.ElapsedTime">
            <summary>
            执行耗时
            </summary>
        </member>
        <member name="P:Admin.Application.RequestLogServices.Dtos.RequestLogOutput.ClientIp">
            <summary>
            客户端IP
            </summary>
        </member>
        <member name="P:Admin.Application.RequestLogServices.Dtos.RequestLogOutput.CreateTime">
            <summary>
            时间
            </summary>
        </member>
        <member name="T:Admin.Application.RequestLogServices.RequestLogService">
            <summary>
            请求日志服务
            </summary>
        </member>
        <member name="M:Admin.Application.RequestLogServices.RequestLogService.#ctor(SqlSugar.ISqlSugarClient)">
            <summary>
            请求日志服务
            </summary>
        </member>
        <member name="M:Admin.Application.RequestLogServices.RequestLogService.GetPagedListAsync(Admin.Application.RequestLogServices.Dtos.GetPagedListInput)">
            <summary>
            分页查询
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.RequestLogServices.RequestLogService.GetRequestLogChartAsync(Admin.Application.RequestLogServices.Dtos.GetRequestLogChartInput)">
            <summary>
            按日期获取请求日志统计数
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:Admin.Application.RoleServices.Dtos.AddRoleInput">
            <summary>
            角色添加
            </summary>
        </member>
        <member name="P:Admin.Application.RoleServices.Dtos.AddRoleInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.Application.RoleServices.Dtos.AddRoleInput.Name">
            <summary>
            角色名称
            </summary>
        </member>
        <member name="P:Admin.Application.RoleServices.Dtos.AddRoleInput.Description">
            <summary>
            角色描述
            </summary>
        </member>
        <member name="P:Admin.Application.RoleServices.Dtos.AddRoleInput.SecurityIds">
            <summary>
            权限Id集合
            </summary>
        </member>
        <member name="T:Admin.Application.RoleServices.Dtos.FunctionOutput">
            <summary>
            组织机构详情
            </summary>
        </member>
        <member name="P:Admin.Application.RoleServices.Dtos.FunctionOutput.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:Admin.Application.RoleServices.Dtos.FunctionOutput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.Application.RoleServices.Dtos.FunctionOutput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:Admin.Application.RoleServices.Dtos.FunctionOutput.Code">
            <summary>
            编码
            </summary>
        </member>
        <member name="P:Admin.Application.RoleServices.Dtos.FunctionOutput.ParentId">
            <summary>
            父级Id
            </summary>
        </member>
        <member name="P:Admin.Application.RoleServices.Dtos.FunctionOutput.Children">
            <summary>
            子集
            </summary>
        </member>
        <member name="T:Admin.Application.RoleServices.Dtos.GetPagedListInput">
            <summary>
            角色查询
            </summary>
        </member>
        <member name="P:Admin.Application.RoleServices.Dtos.GetPagedListInput.Name">
            <summary>
            角色名
            </summary>
        </member>
        <member name="T:Admin.Application.RoleServices.Dtos.RoleOutput">
            <summary>
            角色详情
            </summary>
        </member>
        <member name="P:Admin.Application.RoleServices.Dtos.RoleOutput.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:Admin.Application.RoleServices.Dtos.RoleOutput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.Application.RoleServices.Dtos.RoleOutput.Name">
            <summary>
            角色名称
            </summary>
        </member>
        <member name="P:Admin.Application.RoleServices.Dtos.RoleOutput.Description">
            <summary>
            角色描述
            </summary>
        </member>
        <member name="P:Admin.Application.RoleServices.Dtos.RoleOutput.WebsiteSecurityIds">
            <summary>
            website权限
            </summary>
        </member>
        <member name="P:Admin.Application.RoleServices.Dtos.RoleOutput.InterfaceSecurityIds">
            <summary>
            interface权限
            </summary>
        </member>
        <member name="T:Admin.Application.RoleServices.RoleService">
            <summary>
            角色服务
            </summary>
        </member>
        <member name="M:Admin.Application.RoleServices.RoleService.#ctor(SqlSugar.ISqlSugarClient,Admin.SqlSugar.Repository{Admin.SqlSugar.Entity.RoleEntity},Admin.Core.Cache.IAdminCache)">
            <summary>
            角色服务
            </summary>
        </member>
        <member name="M:Admin.Application.RoleServices.RoleService.GetPagedListAsync(Admin.Application.RoleServices.Dtos.GetPagedListInput)">
            <summary>
            分页查询
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.RoleServices.RoleService.GetRolesAsync(System.String)">
            <summary>
            全量查询
            </summary>
            <param name="roleName"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.RoleServices.RoleService.GetAsync(System.Int64)">
            <summary>
            单条查询
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.RoleServices.RoleService.AddAsync(Admin.Application.RoleServices.Dtos.AddRoleInput)">
            <summary>
            添加
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.RoleServices.RoleService.PutAsync(System.Int64,Admin.Application.RoleServices.Dtos.AddRoleInput)">
            <summary>
            编辑
            </summary>
            <param name="id"></param>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.RoleServices.RoleService.DeleteAsync(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.RoleServices.RoleService.AssignFunctionAsync(System.Int64,System.Int64[])">
            <summary>
            赋给角色功能
            </summary>
            <param name="roleId">角色Id</param>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.RoleServices.RoleService.GetFunctionsAsync(System.Int64)">
            <summary>
            获取角色的功能
            </summary>
        </member>
        <member name="T:Admin.Application.SystemConfigServices.Dtos.AddSystemConfigInput">
            <summary>
            系统配置表添加
            </summary>
        </member>
        <member name="P:Admin.Application.SystemConfigServices.Dtos.AddSystemConfigInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.Application.SystemConfigServices.Dtos.AddSystemConfigInput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:Admin.Application.SystemConfigServices.Dtos.AddSystemConfigInput.ConfigCode">
            <summary>
            编码
            </summary>
        </member>
        <member name="P:Admin.Application.SystemConfigServices.Dtos.AddSystemConfigInput.ConfigValue">
            <summary>
            值
            </summary>
        </member>
        <member name="T:Admin.Application.SystemConfigServices.Dtos.GetPagedListInput">
            <summary>
            系统配置表查询
            </summary>
        </member>
        <member name="P:Admin.Application.SystemConfigServices.Dtos.GetPagedListInput.Name">
            <summary>
            配置名称
            </summary>
        </member>
        <member name="P:Admin.Application.SystemConfigServices.Dtos.GetPagedListInput.ConfigCode">
            <summary>
            配置编码
            </summary>
        </member>
        <member name="T:Admin.Application.SystemConfigServices.Dtos.SystemConfigOutput">
            <summary>
            系统配置表详情
            </summary>
        </member>
        <member name="P:Admin.Application.SystemConfigServices.Dtos.SystemConfigOutput.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:Admin.Application.SystemConfigServices.Dtos.SystemConfigOutput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.Application.SystemConfigServices.Dtos.SystemConfigOutput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:Admin.Application.SystemConfigServices.Dtos.SystemConfigOutput.ConfigCode">
            <summary>
            编码
            </summary>
        </member>
        <member name="P:Admin.Application.SystemConfigServices.Dtos.SystemConfigOutput.ConfigValue">
            <summary>
            值
            </summary>
        </member>
        <member name="T:Admin.Application.SystemConfigServices.SystemConfigService">
            <summary>
            系统配置表服务
            </summary>
        </member>
        <member name="M:Admin.Application.SystemConfigServices.SystemConfigService.#ctor(SqlSugar.ISqlSugarClient,Admin.SqlSugar.Repository{Admin.SqlSugar.Entity.SystemConfigEntity})">
            <summary>
            系统配置表服务
            </summary>
        </member>
        <member name="M:Admin.Application.SystemConfigServices.SystemConfigService.GetPagedListAsync(Admin.Application.SystemConfigServices.Dtos.GetPagedListInput)">
            <summary>
            分页查询
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.SystemConfigServices.SystemConfigService.GetAsync(System.Int64)">
            <summary>
            单条查询
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.SystemConfigServices.SystemConfigService.AddAsync(Admin.Application.SystemConfigServices.Dtos.AddSystemConfigInput)">
            <summary>
            添加
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.SystemConfigServices.SystemConfigService.PutAsync(System.Int64,Admin.Application.SystemConfigServices.Dtos.AddSystemConfigInput)">
            <summary>
            编辑
            </summary>
            <param name="id"></param>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.SystemConfigServices.SystemConfigService.DeleteAsync(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="T:Admin.Application.UserServices.Dtos.AddUserInput">
            <summary>
            用户添加
            </summary>
        </member>
        <member name="P:Admin.Application.UserServices.Dtos.AddUserInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.Application.UserServices.Dtos.AddUserInput.Account">
            <summary>
            账号
            </summary>
        </member>
        <member name="P:Admin.Application.UserServices.Dtos.AddUserInput.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:Admin.Application.UserServices.Dtos.AddUserInput.Name">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:Admin.Application.UserServices.Dtos.AddUserInput.Telephone">
            <summary>
            电话
            </summary>
        </member>
        <member name="P:Admin.Application.UserServices.Dtos.AddUserInput.Email">
            <summary>
            邮箱
            </summary>
        </member>
        <member name="P:Admin.Application.UserServices.Dtos.AddUserInput.Avatar">
            <summary>
            头像
            </summary>
        </member>
        <member name="P:Admin.Application.UserServices.Dtos.AddUserInput.OrganizationId">
            <summary>
            组织机构Id
            </summary>
        </member>
        <member name="P:Admin.Application.UserServices.Dtos.AddUserInput.RoleId">
            <summary>
            角色Id
            </summary>
        </member>
        <member name="T:Admin.Application.UserServices.Dtos.GetPagedListInput">
            <summary>
            用户查询
            </summary>
        </member>
        <member name="P:Admin.Application.UserServices.Dtos.GetPagedListInput.Account">
            <summary>
            账号
            </summary>
        </member>
        <member name="P:Admin.Application.UserServices.Dtos.GetPagedListInput.Name">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:Admin.Application.UserServices.Dtos.GetPagedListInput.Telephone">
            <summary>
            电话
            </summary>
        </member>
        <member name="P:Admin.Application.UserServices.Dtos.GetPagedListInput.Email">
            <summary>
            邮箱
            </summary>
        </member>
        <member name="P:Admin.Application.UserServices.Dtos.GetPagedListInput.Status">
            <summary>
            账户状态
            </summary>
        </member>
        <member name="T:Admin.Application.UserServices.Dtos.PutUserInput">
            <summary>
            用户编辑
            </summary>
        </member>
        <member name="P:Admin.Application.UserServices.Dtos.PutUserInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.Application.UserServices.Dtos.PutUserInput.Name">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:Admin.Application.UserServices.Dtos.PutUserInput.Telephone">
            <summary>
            电话
            </summary>
        </member>
        <member name="P:Admin.Application.UserServices.Dtos.PutUserInput.Email">
            <summary>
            邮箱
            </summary>
        </member>
        <member name="P:Admin.Application.UserServices.Dtos.PutUserInput.Avatar">
            <summary>
            头像
            </summary>
        </member>
        <member name="P:Admin.Application.UserServices.Dtos.PutUserInput.OrganizationId">
            <summary>
            组织机构Id
            </summary>
        </member>
        <member name="P:Admin.Application.UserServices.Dtos.PutUserInput.RoleId">
            <summary>
            角色Id
            </summary>
        </member>
        <member name="T:Admin.Application.UserServices.Dtos.UserOutput">
            <summary>
            用户详情
            </summary>
        </member>
        <member name="P:Admin.Application.UserServices.Dtos.UserOutput.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:Admin.Application.UserServices.Dtos.UserOutput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.Application.UserServices.Dtos.UserOutput.Account">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:Admin.Application.UserServices.Dtos.UserOutput.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:Admin.Application.UserServices.Dtos.UserOutput.Name">
            <summary>
            真实姓名
            </summary>
        </member>
        <member name="P:Admin.Application.UserServices.Dtos.UserOutput.Telephone">
            <summary>
            电话
            </summary>
        </member>
        <member name="P:Admin.Application.UserServices.Dtos.UserOutput.Email">
            <summary>
            邮箱
            </summary>
        </member>
        <member name="P:Admin.Application.UserServices.Dtos.UserOutput.Avatar">
            <summary>
            头像
            </summary>
        </member>
        <member name="P:Admin.Application.UserServices.Dtos.UserOutput.OrganizationId">
            <summary>
            组织机构Id
            </summary>
        </member>
        <member name="P:Admin.Application.UserServices.Dtos.UserOutput.OrganizationName">
            <summary>
            组织机构名称
            </summary>
        </member>
        <member name="P:Admin.Application.UserServices.Dtos.UserOutput.RoleId">
            <summary>
            角色Id
            </summary>
        </member>
        <member name="P:Admin.Application.UserServices.Dtos.UserOutput.RoleName">
            <summary>
            角色名称
            </summary>
        </member>
        <member name="P:Admin.Application.UserServices.Dtos.UserOutput.Status">
            <summary>
            用户状态
            </summary>
        </member>
        <member name="T:Admin.Application.UserServices.Mapper.AddUserInputToEntity">
            <summary>
            新增用户映射
            </summary>
        </member>
        <member name="T:Admin.Application.UserServices.UserService">
            <summary>
            用户服务
            </summary>
        </member>
        <member name="M:Admin.Application.UserServices.UserService.#ctor(SqlSugar.ISqlSugarClient,Admin.SqlSugar.Repository{Admin.SqlSugar.Entity.UserEntity},Microsoft.AspNetCore.SignalR.IHubContext{Admin.Multiplex.AdminUser.OnlineUserHub,Admin.Multiplex.Contracts.IAdminUser.IOnlineUserClient},Admin.Multiplex.Contracts.IAdminUser.ICacheOnlineUser)">
            <summary>
            用户服务
            </summary>
        </member>
        <member name="M:Admin.Application.UserServices.UserService.GetPagedListAsync(Admin.Application.UserServices.Dtos.GetPagedListInput)">
            <summary>
            分页查询
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.UserServices.UserService.GetAsync(System.Int64)">
            <summary>
            单条查询
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.UserServices.UserService.AddAsync(Admin.Application.UserServices.Dtos.AddUserInput)">
            <summary>
            添加
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.UserServices.UserService.PutAsync(System.Int64,Admin.Application.UserServices.Dtos.PutUserInput)">
            <summary>
            编辑
            </summary>
            <param name="id"></param>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.UserServices.UserService.DeleteAsync(System.Int64)">
            <summary>
            删除
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.UserServices.UserService.StopAsync(System.Int64)">
            <summary>
            账户停用
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.UserServices.UserService.NormalAsync(System.Int64)">
            <summary>
            账户恢复正常
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.Application.UserServices.UserService.ResetPasswordAsync(System.Int64)">
            <summary>
            重置密码
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
    </members>
</doc>
